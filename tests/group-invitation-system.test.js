const request = require("supertest");
const mongoose = require("mongoose");
const app = require("../server"); // Adjust path to your app
const User = require("../models/User/User");
const Invitation = require("../models/User/invitations");
const Chat = require("../models/Common/Chat");

describe("Group Invitation System", () => {
  let creator, user1, user2, user3;
  let creatorToken, user1Token, user2Token, user3Token;
  let groupInvitation;

  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect(
      process.env.TEST_MONGODB_URI || "mongodb://localhost:27017/netme_test"
    );
  });

  beforeEach(async () => {
    // Clean up database
    await User.deleteMany({});
    await Invitation.deleteMany({});
    await Chat.deleteMany({});

    // Create test users
    creator = await User.create({
      userName: "creator",
      email: "<EMAIL>",
      password: "password123",
      isPremium: false,
    });

    user1 = await User.create({
      userName: "user1",
      email: "<EMAIL>",
      password: "password123",
      isPremium: false,
    });

    user2 = await User.create({
      userName: "user2",
      email: "<EMAIL>",
      password: "password123",
      isPremium: false,
    });

    user3 = await User.create({
      userName: "user3",
      email: "<EMAIL>",
      password: "password123",
      isPremium: false,
    });

    // Generate tokens (mock implementation - adjust based on your auth system)
    creatorToken = "mock-token-creator";
    user1Token = "mock-token-user1";
    user2Token = "mock-token-user2";
    user3Token = "mock-token-user3";
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  describe("Invitation Creation and Acceptance", () => {
    test("should create group invitation with multiple users", async () => {
      const invitationData = {
        users: [
          { userId: user1._id },
          { userId: user2._id },
          { userId: user3._id },
        ],
        groupName: "Test Group",
        date: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        time: "14:00",
        isGroup: true,
      };

      const response = await request(app)
        .post("/api/invitations")
        .set("Authorization", `Bearer ${creatorToken}`)
        .send(invitationData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.invitation.isGroup).toBe(true);
      expect(response.body.invitation.users).toHaveLength(3);

      groupInvitation = response.body.invitation;
    });

    test("should track acceptedAt timestamp when user accepts invitation", async () => {
      // First create invitation
      groupInvitation = await Invitation.create({
        invitationBy: creator._id,
        users: [
          { userId: user1._id, status: "Pending" },
          { userId: user2._id, status: "Pending" },
          { userId: user3._id, status: "Pending" },
        ],
        groupName: "Test Group",
        date: new Date(Date.now() + 24 * 60 * 60 * 1000),
        time: "14:00",
        isGroup: true,
      });

      const beforeAcceptance = new Date();

      // User1 accepts invitation
      const response = await request(app)
        .patch(`/api/invitations/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${user1Token}`)
        .send({ status: "Accepted" })
        .expect(200);

      const afterAcceptance = new Date();

      // Check that acceptedAt timestamp was set
      const updatedInvitation = await Invitation.findById(groupInvitation._id);
      const user1Data = updatedInvitation.users.find(
        (u) => u.userId.toString() === user1._id.toString()
      );

      expect(user1Data.status).toBe("Accepted");
      expect(user1Data.acceptedAt).toBeDefined();
      expect(user1Data.acceptedAt).toBeInstanceOf(Date);
      expect(user1Data.acceptedAt.getTime()).toBeGreaterThanOrEqual(
        beforeAcceptance.getTime()
      );
      expect(user1Data.acceptedAt.getTime()).toBeLessThanOrEqual(
        afterAcceptance.getTime()
      );
    });
  });

  describe("Chat Participant Management", () => {
    beforeEach(async () => {
      // Create a group invitation
      groupInvitation = await Invitation.create({
        invitationBy: creator._id,
        users: [
          { userId: user1._id, status: "Pending" },
          { userId: user2._id, status: "Pending" },
          { userId: user3._id, status: "Pending" },
        ],
        groupName: "Test Group",
        date: new Date(Date.now() + 24 * 60 * 60 * 1000),
        time: "14:00",
        isGroup: true,
      });
    });

    test("should only include accepted users in chat participants", async () => {
      // User1 accepts invitation
      await request(app)
        .patch(`/api/invitations/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${user1Token}`)
        .send({ status: "Accepted" });

      // Initialize chat
      const response = await request(app)
        .post(`/api/chats/initialize/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${creatorToken}`)
        .expect(201);

      const chat = await Chat.findById(response.body.chat._id);

      // Should only have creator and user1 (who accepted)
      expect(chat.participants).toHaveLength(2);

      const participantIds = chat.participants.map((p) => p.userId.toString());
      expect(participantIds).toContain(creator._id.toString());
      expect(participantIds).toContain(user1._id.toString());
      expect(participantIds).not.toContain(user2._id.toString());
      expect(participantIds).not.toContain(user3._id.toString());
    });

    test("should add user to chat when they accept invitation after chat creation", async () => {
      // User1 accepts first
      await request(app)
        .patch(`/api/invitations/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${user1Token}`)
        .send({ status: "Accepted" });

      // Initialize chat
      const initResponse = await request(app)
        .post(`/api/chats/initialize/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${creatorToken}`)
        .expect(201);

      // User2 accepts later - this should automatically add them to chat participants
      await request(app)
        .patch(`/api/invitations/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${user2Token}`)
        .send({ status: "Accepted" });

      // Wait a bit for the async chat update to complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      const chat = await Chat.findById(initResponse.body.chat._id);

      // Should now have creator, user1, and user2
      expect(chat.participants).toHaveLength(3);

      const participantIds = chat.participants.map((p) => p.userId.toString());
      expect(participantIds).toContain(creator._id.toString());
      expect(participantIds).toContain(user1._id.toString());
      expect(participantIds).toContain(user2._id.toString());
    });

    test("should track joinedAt timestamp for each participant", async () => {
      const beforeUser1Accept = new Date();

      // User1 accepts invitation
      await request(app)
        .patch(`/api/invitations/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${user1Token}`)
        .send({ status: "Accepted" });

      const afterUser1Accept = new Date();

      // Initialize chat
      const response = await request(app)
        .post(`/api/chats/initialize/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${creatorToken}`)
        .expect(201);

      const chat = await Chat.findById(response.body.chat._id);

      // Check joinedAt timestamps
      const creatorParticipant = chat.participants.find(
        (p) => p.userId.toString() === creator._id.toString()
      );
      const user1Participant = chat.participants.find(
        (p) => p.userId.toString() === user1._id.toString()
      );

      expect(creatorParticipant.joinedAt).toBeDefined();
      expect(user1Participant.joinedAt).toBeDefined();

      // User1's joinedAt should be around their acceptance time
      expect(user1Participant.joinedAt.getTime()).toBeGreaterThanOrEqual(
        beforeUser1Accept.getTime()
      );
      expect(user1Participant.joinedAt.getTime()).toBeLessThanOrEqual(
        afterUser1Accept.getTime()
      );
    });
  });

  describe("Message Visibility and Filtering", () => {
    let chat;

    beforeEach(async () => {
      // Create invitation and have user1 accept
      groupInvitation = await Invitation.create({
        invitationBy: creator._id,
        users: [
          { userId: user1._id, status: "Accepted", acceptedAt: new Date() },
          { userId: user2._id, status: "Pending" },
          { userId: user3._id, status: "Pending" },
        ],
        groupName: "Test Group",
        date: new Date(Date.now() + 24 * 60 * 60 * 1000),
        time: "14:00",
        isGroup: true,
      });

      // Create chat with proper participant structure
      chat = await Chat.create({
        invitationId: groupInvitation._id,
        participants: [
          { userId: creator._id, joinedAt: groupInvitation.createdAt },
          { userId: user1._id, joinedAt: new Date() },
        ],
        isGroupChat: true,
        messages: [],
      });
    });

    test("should only show messages from when user joined forward", async () => {
      const message1Time = new Date();

      // Creator sends first message
      await request(app)
        .post(`/api/chats/${chat._id}/messages`)
        .set("Authorization", `Bearer ${creatorToken}`)
        .send({ content: "First message" })
        .expect(201);

      // Wait a bit
      await new Promise((resolve) => setTimeout(resolve, 100));

      // User2 accepts invitation (joins later) - should automatically add to chat
      await request(app)
        .patch(`/api/invitations/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${user2Token}`)
        .send({ status: "Accepted" });

      // Wait for async chat update
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Creator sends second message after user2 joined
      await request(app)
        .post(`/api/chats/${chat._id}/messages`)
        .set("Authorization", `Bearer ${creatorToken}`)
        .send({ content: "Second message" })
        .expect(201);

      // User1 should see both messages (was there from the beginning)
      const user1Messages = await request(app)
        .get(`/api/chats/${chat._id}/messages`)
        .set("Authorization", `Bearer ${user1Token}`)
        .expect(200);

      expect(user1Messages.body.messages).toHaveLength(2);

      // User2 should only see the second message (joined after first message)
      const user2Messages = await request(app)
        .get(`/api/chats/${chat._id}/messages`)
        .set("Authorization", `Bearer ${user2Token}`)
        .expect(200);

      expect(user2Messages.body.messages).toHaveLength(1);
      expect(user2Messages.body.messages[0].content).toBe("Second message");
    });

    test("should not broadcast messages to pending users", async () => {
      // This test would require mocking the socket service
      // For now, we'll test that pending users can't access the chat

      const response = await request(app)
        .get(`/api/chats/${chat._id}/messages`)
        .set("Authorization", `Bearer ${user2Token}`) // user2 is still pending
        .expect(404); // Should not find chat for pending user

      expect(response.body.success).toBe(false);
    });

    test("should prevent pending users from sending messages", async () => {
      const response = await request(app)
        .post(`/api/chats/${chat._id}/messages`)
        .set("Authorization", `Bearer ${user2Token}`) // user2 is still pending
        .send({ content: "Should not work" })
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain("Not authorized");
    });
  });

  describe("Edge Cases and Re-invitations", () => {
    test("should handle user accepting invitation multiple times", async () => {
      groupInvitation = await Invitation.create({
        invitationBy: creator._id,
        users: [{ userId: user1._id, status: "Pending" }],
        groupName: "Test Group",
        date: new Date(Date.now() + 24 * 60 * 60 * 1000),
        time: "14:00",
        isGroup: true,
      });

      // Accept invitation first time
      await request(app)
        .patch(`/api/invitations/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${user1Token}`)
        .send({ status: "Accepted" })
        .expect(200);

      // Try to accept again - should not cause errors
      await request(app)
        .patch(`/api/invitations/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${user1Token}`)
        .send({ status: "Accepted" })
        .expect(200);

      const invitation = await Invitation.findById(groupInvitation._id);
      const user1Data = invitation.users.find(
        (u) => u.userId.toString() === user1._id.toString()
      );

      expect(user1Data.status).toBe("Accepted");
      expect(user1Data.acceptedAt).toBeDefined();
    });

    test("should handle user rejecting then accepting invitation", async () => {
      groupInvitation = await Invitation.create({
        invitationBy: creator._id,
        users: [{ userId: user1._id, status: "Pending" }],
        groupName: "Test Group",
        date: new Date(Date.now() + 24 * 60 * 60 * 1000),
        time: "14:00",
        isGroup: true,
      });

      // Reject invitation first
      await request(app)
        .patch(`/api/invitations/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${user1Token}`)
        .send({ status: "Rejected" })
        .expect(200);

      // Then accept it
      await request(app)
        .patch(`/api/invitations/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${user1Token}`)
        .send({ status: "Accepted" })
        .expect(200);

      const invitation = await Invitation.findById(groupInvitation._id);
      const user1Data = invitation.users.find(
        (u) => u.userId.toString() === user1._id.toString()
      );

      expect(user1Data.status).toBe("Accepted");
      expect(user1Data.acceptedAt).toBeDefined();
    });

    test("should maintain message history integrity when users join at different times", async () => {
      // Create invitation with multiple users
      groupInvitation = await Invitation.create({
        invitationBy: creator._id,
        users: [
          { userId: user1._id, status: "Pending" },
          { userId: user2._id, status: "Pending" },
          { userId: user3._id, status: "Pending" },
        ],
        groupName: "Test Group",
        date: new Date(Date.now() + 24 * 60 * 60 * 1000),
        time: "14:00",
        isGroup: true,
      });

      // User1 accepts and chat is created
      await request(app)
        .patch(`/api/invitations/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${user1Token}`)
        .send({ status: "Accepted" });

      const chatResponse = await request(app)
        .post(`/api/chats/initialize/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${creatorToken}`)
        .expect(201);

      const chatId = chatResponse.body.chat._id;

      // Send some messages
      await request(app)
        .post(`/api/chats/${chatId}/messages`)
        .set("Authorization", `Bearer ${creatorToken}`)
        .send({ content: "Message 1" });

      await request(app)
        .post(`/api/chats/${chatId}/messages`)
        .set("Authorization", `Bearer ${user1Token}`)
        .send({ content: "Message 2" });

      // User2 joins later - should automatically add to chat
      await request(app)
        .patch(`/api/invitations/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${user2Token}`)
        .send({ status: "Accepted" });

      // Wait for async chat update
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Send more messages
      await request(app)
        .post(`/api/chats/${chatId}/messages`)
        .set("Authorization", `Bearer ${creatorToken}`)
        .send({ content: "Message 3" });

      // User3 joins even later - should automatically add to chat
      await request(app)
        .patch(`/api/invitations/${groupInvitation._id}`)
        .set("Authorization", `Bearer ${user3Token}`)
        .send({ status: "Accepted" });

      // Wait for async chat update
      await new Promise((resolve) => setTimeout(resolve, 100));

      await request(app)
        .post(`/api/chats/${chatId}/messages`)
        .set("Authorization", `Bearer ${creatorToken}`)
        .send({ content: "Message 4" });

      // Verify message visibility
      const user1Messages = await request(app)
        .get(`/api/chats/${chatId}/messages`)
        .set("Authorization", `Bearer ${user1Token}`)
        .expect(200);

      const user2Messages = await request(app)
        .get(`/api/chats/${chatId}/messages`)
        .set("Authorization", `Bearer ${user2Token}`)
        .expect(200);

      const user3Messages = await request(app)
        .get(`/api/chats/${chatId}/messages`)
        .set("Authorization", `Bearer ${user3Token}`)
        .expect(200);

      // User1 should see messages 1, 2, 3, 4 (was there from the beginning)
      expect(user1Messages.body.messages.length).toBeGreaterThanOrEqual(3);

      // User2 should see messages 3, 4 (joined after message 2)
      expect(user2Messages.body.messages.length).toBeGreaterThanOrEqual(1);
      expect(user2Messages.body.messages.length).toBeLessThan(
        user1Messages.body.messages.length
      );

      // User3 should see only message 4 (joined after message 3)
      expect(user3Messages.body.messages).toHaveLength(1);
      expect(user3Messages.body.messages[0].content).toBe("Message 4");
    });
  });
});
