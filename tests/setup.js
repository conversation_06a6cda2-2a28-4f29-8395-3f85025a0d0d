/**
 * Jest Test Setup
 * 
 * Global setup for timezone tests
 */

// Force UTC timezone for all tests
process.env.TZ = 'UTC';

// Mock console methods to reduce noise during tests
global.console = {
  ...console,
  // Uncomment to suppress logs during tests
  // log: jest.fn(),
  // info: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
};

// Global test utilities
global.testUtils = {
  /**
   * Create a test date in UTC
   */
  createTestDate: (year, month, day, hour = 0, minute = 0, second = 0) => {
    return new Date(Date.UTC(year, month - 1, day, hour, minute, second));
  },
  
  /**
   * Assert that a date is in UTC format
   */
  expectUTCDate: (date) => {
    expect(date).toBeInstanceOf(Date);
    expect(date.toISOString()).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
  },
  
  /**
   * Assert that a string is in ISO 8601 UTC format
   */
  expectUTCString: (dateString) => {
    expect(typeof dateString).toBe('string');
    expect(dateString).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
    expect(dateString.endsWith('Z')).toBe(true);
  }
};

// Set up test database connection if needed
beforeAll(async () => {
  // Any global setup can go here
  console.log('🧪 Starting timezone tests with UTC timezone');
  console.log(`📅 Test started at: ${new Date().toISOString()}`);
});

afterAll(async () => {
  // Any global cleanup can go here
  console.log('✅ Timezone tests completed');
});
