# Timezone Testing Suite

Comprehensive tests to verify all time operations work correctly across different timezones and ensure 100% UTC consistency throughout the application.

## Overview

This testing suite validates that the backend properly handles timezone operations and maintains UTC consistency across all components including:

- Core time utility functions
- API response formatting
- Database operations
- Cron job scheduling
- Socket service operations
- Cross-timezone compatibility

## Test Structure

### Core Test Files

- `timezone.test.js` - Main test suite for timezone functionality
- `jest.config.js` - Jest configuration for timezone tests
- `setup.js` - Global test setup and utilities

### Test Categories

1. **Core Time Functions**
   - `now()`, `nowISO()`, `createUTCDateTime()`
   - Basic UTC time operations

2. **Date Range Functions**
   - `getStartOfDayUTC()`, `getEndOfDayUTC()`
   - UTC date range calculations

3. **Date Arithmetic**
   - `addTime()`, `subtractTime()`, `getTimeDifference()`
   - Time calculations and differences

4. **Date Checking Functions**
   - `isFuture()`, `isPast()`, `isToday()`
   - Date validation and comparison

5. **Formatting and Parsing**
   - `formatForAPI()`, `parseTimeString()`
   - String formatting and parsing

6. **Cross-Timezone Consistency**
   - Server timezone independence
   - Daylight saving time handling
   - Leap year calculations

7. **Integration Tests**
   - API response formatting
   - Middleware functionality
   - Database consistency

## Running Tests

### Prerequisites

Install Jest if not already installed:
```bash
npm install --save-dev jest
```

### Run All Tests
```bash
npm test
```

### Run Timezone Tests Only
```bash
npx jest tests/timezone.test.js
```

### Run Tests with Coverage
```bash
npx jest --coverage
```

### Run Tests in Watch Mode
```bash
npx jest --watch
```

### Run Tests with Verbose Output
```bash
npx jest --verbose
```

## Test Configuration

### Jest Configuration (`jest.config.js`)

- **Test Environment**: Node.js
- **Coverage Threshold**: 80% overall, 95% for timeUtils
- **Test Timeout**: 10 seconds
- **Coverage Reports**: Text, LCOV, HTML

### Environment Setup (`setup.js`)

- Forces UTC timezone for all tests
- Provides global test utilities
- Sets up consistent test environment

## Test Utilities

Global utilities available in all tests:

```javascript
// Create a test date in UTC
const testDate = testUtils.createTestDate(2024, 1, 15, 14, 30, 0);

// Assert UTC date format
testUtils.expectUTCDate(someDate);

// Assert UTC string format
testUtils.expectUTCString(someISOString);
```

## Coverage Requirements

### Overall Coverage: 80%
- Branches: 80%
- Functions: 80%
- Lines: 80%
- Statements: 80%

### Critical Files: 95%
- `util/timeUtils.js`: 95% coverage required
- Core timezone functionality must be thoroughly tested

## Test Scenarios

### 1. Basic Functionality Tests
```javascript
test('now() should return current UTC time', () => {
  const currentTime = now();
  expect(currentTime).toBeInstanceOf(Date);
});
```

### 2. Cross-Timezone Tests
```javascript
test('should maintain UTC consistency regardless of server timezone', () => {
  // Tests with different TZ environment variables
  // Ensures all results are UTC regardless of server timezone
});
```

### 3. Edge Case Tests
```javascript
test('should handle daylight saving time transitions correctly', () => {
  // Tests around DST transitions
  // Ensures consistent behavior during time changes
});
```

### 4. Integration Tests
```javascript
test('formatTimeResponse middleware should format dates in responses', () => {
  // Tests middleware functionality
  // Ensures API responses are properly formatted
});
```

## Expected Test Results

### Successful Run Output
```
🧪 Starting timezone tests with UTC timezone
📅 Test started at: 2024-01-15T10:30:00.000Z

 PASS  tests/timezone.test.js
  Timezone Utilities
    Core Time Functions
      ✓ now() should return current UTC time (2 ms)
      ✓ nowISO() should return ISO 8601 UTC string (1 ms)
      ✓ createUTCDateTime() should create proper UTC date (1 ms)
    Date Range Functions
      ✓ getStartOfDayUTC() should return start of day in UTC (1 ms)
      ✓ getEndOfDayUTC() should return end of day in UTC (1 ms)
    ...

Test Suites: 1 passed, 1 total
Tests:       45 passed, 45 total
Snapshots:   0 total
Time:        2.345 s

✅ Timezone tests completed
```

### Coverage Report
```
File                              | % Stmts | % Branch | % Funcs | % Lines
----------------------------------|---------|----------|---------|--------
All files                         |   85.23 |    82.14 |   87.45 |   84.67
 util/timeUtils.js               |   96.78 |    95.23 |   98.45 |   97.12
 middleware/formatTimeResponse.js |   89.34 |    85.67 |   91.23 |   88.78
 controllers/                     |   82.45 |    79.34 |   84.56 |   81.89
 services/                        |   78.23 |    75.67 |   80.12 |   77.45
```

## Troubleshooting

### Common Issues

1. **Timezone Environment Issues**
   ```
   Error: Tests failing due to timezone differences
   ```
   - Ensure `TZ=UTC` is set in test environment
   - Check that `setup.js` is properly configured

2. **Date Parsing Issues**
   ```
   Error: Invalid date format in tests
   ```
   - Use `testUtils.createTestDate()` for consistent date creation
   - Ensure all test dates are in UTC format

3. **Coverage Issues**
   ```
   Error: Coverage threshold not met
   ```
   - Add tests for uncovered branches
   - Focus on edge cases and error handling

### Debugging Tests

Enable verbose logging:
```javascript
// In test files
console.log('Debug info:', {
  date: someDate,
  formatted: formatForAPI(someDate),
  timezone: process.env.TZ
});
```

Run specific test:
```bash
npx jest --testNamePattern="should handle daylight saving time"
```

## Continuous Integration

### GitHub Actions Example
```yaml
- name: Run Timezone Tests
  run: |
    export TZ=UTC
    npm test -- --coverage
    
- name: Upload Coverage
  uses: codecov/codecov-action@v1
  with:
    file: ./coverage/lcov.info
```

### Pre-commit Hook
```bash
#!/bin/sh
# Run timezone tests before commit
export TZ=UTC
npm run test:timezone
```

## Best Practices

1. **Always Use UTC in Tests**
   - Set `TZ=UTC` environment variable
   - Use `testUtils.createTestDate()` for consistent dates

2. **Test Edge Cases**
   - Daylight saving time transitions
   - Leap years and month boundaries
   - Invalid date inputs

3. **Verify API Responses**
   - Test middleware formatting
   - Ensure ISO 8601 UTC format
   - Validate nested date objects

4. **Performance Testing**
   - Test with large datasets
   - Verify reasonable execution times
   - Monitor memory usage

## Related Documentation

- [Timezone Utilities](../util/timeUtils.js)
- [Migration Scripts](../migrations/README.md)
- [API Response Formatting](../middleware/formatTimeResponse.js)
