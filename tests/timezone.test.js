/**
 * Comprehensive Timezone Tests
 *
 * Tests to verify all time operations work correctly across different timezones
 * and ensure 100% UTC consistency throughout the application.
 */

const {
  now,
  nowISO,
  createUTCDateTime,
  getStartOfDayUTC,
  getEndOfDayUTC,
  addTime,
  subtractTime,
  getTimeDifference,
  isFuture,
  isPast,
  isToday,
  formatForAPI,
  parseTimeString,
  validateTimezone,
  createExpiryDate,
} = require("../util/timeUtils");

describe("Timezone Utilities", () => {
  describe("Core Time Functions", () => {
    test("now() should return current UTC time", () => {
      const currentTime = now();
      const jsDate = new Date();

      expect(currentTime).toBeInstanceOf(Date);
      // Should be within 1 second of current time
      expect(Math.abs(currentTime.getTime() - jsDate.getTime())).toBeLessThan(
        1000
      );
    });

    test("nowISO() should return ISO 8601 UTC string", () => {
      const isoString = nowISO();

      expect(typeof isoString).toBe("string");
      expect(isoString).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/
      );
      expect(isoString.endsWith("Z")).toBe(true);
    });

    test("createUTCDateTime() should create proper UTC date", () => {
      const utcDate = createUTCDateTime("2024-01-15", "14:30");

      expect(utcDate).toBeInstanceOf(Date);
      expect(utcDate.getUTCFullYear()).toBe(2024);
      expect(utcDate.getUTCMonth()).toBe(0); // January is 0
      expect(utcDate.getUTCDate()).toBe(15);
      expect(utcDate.getUTCHours()).toBe(14);
      expect(utcDate.getUTCMinutes()).toBe(30);
    });

    test("createUTCDateTime() should handle edge cases", () => {
      expect(() => createUTCDateTime("", "14:30")).toThrow();
      expect(() => createUTCDateTime("2024-01-15", "")).toThrow();
      expect(() => createUTCDateTime("2024-01-15", "25:30")).toThrow();
      expect(() => createUTCDateTime("2024-01-15", "14:70")).toThrow();
    });
  });

  describe("Date Range Functions", () => {
    test("getStartOfDayUTC() should return start of day in UTC", () => {
      const testDate = new Date("2024-01-15T14:30:45.123Z");
      const startOfDay = getStartOfDayUTC(testDate);

      expect(startOfDay.getUTCFullYear()).toBe(2024);
      expect(startOfDay.getUTCMonth()).toBe(0);
      expect(startOfDay.getUTCDate()).toBe(15);
      expect(startOfDay.getUTCHours()).toBe(0);
      expect(startOfDay.getUTCMinutes()).toBe(0);
      expect(startOfDay.getUTCSeconds()).toBe(0);
      expect(startOfDay.getUTCMilliseconds()).toBe(0);
    });

    test("getEndOfDayUTC() should return end of day in UTC", () => {
      const testDate = new Date("2024-01-15T14:30:45.123Z");
      const endOfDay = getEndOfDayUTC(testDate);

      expect(endOfDay.getUTCFullYear()).toBe(2024);
      expect(endOfDay.getUTCMonth()).toBe(0);
      expect(endOfDay.getUTCDate()).toBe(15);
      expect(endOfDay.getUTCHours()).toBe(23);
      expect(endOfDay.getUTCMinutes()).toBe(59);
      expect(endOfDay.getUTCSeconds()).toBe(59);
      expect(endOfDay.getUTCMilliseconds()).toBe(999);
    });
  });

  describe("Date Arithmetic", () => {
    test("addTime() should add time correctly", () => {
      const baseDate = new Date("2024-01-15T12:00:00.000Z");

      const plus1Hour = addTime(baseDate, 1, "hours");
      expect(plus1Hour.getUTCHours()).toBe(13);

      const plus30Minutes = addTime(baseDate, 30, "minutes");
      expect(plus30Minutes.getUTCMinutes()).toBe(30);

      const plus1Day = addTime(baseDate, 1, "days");
      expect(plus1Day.getUTCDate()).toBe(16);

      const plus1Month = addTime(baseDate, 1, "months");
      expect(plus1Month.getUTCMonth()).toBe(1); // February
    });

    test("subtractTime() should subtract time correctly", () => {
      const baseDate = new Date("2024-01-15T12:00:00.000Z");

      const minus1Hour = subtractTime(baseDate, 1, "hours");
      expect(minus1Hour.getUTCHours()).toBe(11);

      const minus30Minutes = subtractTime(baseDate, 30, "minutes");
      expect(minus30Minutes.getUTCMinutes()).toBe(30);
      expect(minus30Minutes.getUTCHours()).toBe(11);

      const minus1Day = subtractTime(baseDate, 1, "days");
      expect(minus1Day.getUTCDate()).toBe(14);
    });

    test("getTimeDifference() should calculate differences correctly", () => {
      const date1 = new Date("2024-01-15T12:00:00.000Z");
      const date2 = new Date("2024-01-15T14:30:00.000Z");

      expect(getTimeDifference(date2, date1, "hours")).toBe(2);
      expect(getTimeDifference(date2, date1, "minutes")).toBe(150);
      expect(getTimeDifference(date1, date2, "hours")).toBe(-2);
    });
  });

  describe("Date Checking Functions", () => {
    test("isFuture() should correctly identify future dates", () => {
      const futureDate = addTime(now(), 1, "hours");
      const pastDate = subtractTime(now(), 1, "hours");

      expect(isFuture(futureDate)).toBe(true);
      expect(isFuture(pastDate)).toBe(false);
    });

    test("isPast() should correctly identify past dates", () => {
      const futureDate = addTime(now(), 1, "hours");
      const pastDate = subtractTime(now(), 1, "hours");

      expect(isPast(pastDate)).toBe(true);
      expect(isPast(futureDate)).toBe(false);
    });

    test("isToday() should correctly identify today dates", () => {
      const today = now();
      const tomorrow = addTime(today, 1, "days");
      const yesterday = subtractTime(today, 1, "days");

      expect(isToday(today)).toBe(true);
      expect(isToday(tomorrow)).toBe(false);
      expect(isToday(yesterday)).toBe(false);
    });
  });

  describe("Formatting and Parsing", () => {
    test("formatForAPI() should return ISO 8601 UTC string", () => {
      const testDate = new Date("2024-01-15T14:30:45.123Z");
      const formatted = formatForAPI(testDate);

      expect(formatted).toBe("2024-01-15T14:30:45.123Z");
      expect(formatForAPI(null)).toBe(null);
      expect(formatForAPI(undefined)).toBe(null);
    });

    test("parseTimeString() should parse various time formats", () => {
      expect(parseTimeString("14:30")).toEqual([14, 30]);
      expect(parseTimeString("9:05")).toEqual([9, 5]);
      expect(parseTimeString("TimeOfDay(14:30)")).toEqual([14, 30]);
      expect(parseTimeString("invalid")).toBe(null);
      expect(parseTimeString("25:30")).toBe(null);
      expect(parseTimeString("14:70")).toBe(null);
    });

    test("validateTimezone() should validate IANA timezone identifiers", () => {
      expect(validateTimezone("UTC")).toBe(true);
      expect(validateTimezone("America/New_York")).toBe(true);
      expect(validateTimezone("Europe/London")).toBe(true);
      expect(validateTimezone("Asia/Tokyo")).toBe(true);
      expect(validateTimezone("Invalid/Timezone")).toBe(false);
      expect(validateTimezone("")).toBe(false);
      expect(validateTimezone(null)).toBe(false);
    });
  });

  describe("Utility Functions", () => {
    test("createExpiryDate() should create proper expiry dates", () => {
      const baseDate = new Date("2024-01-15T12:00:00.000Z");

      const expiry1Hour = createExpiryDate(baseDate, 1, "hours");
      expect(expiry1Hour.getUTCHours()).toBe(13);

      const expiry7Days = createExpiryDate(baseDate, 7, "days");
      expect(expiry7Days.getUTCDate()).toBe(22);
    });
  });

  describe("Cross-Timezone Consistency", () => {
    test("should maintain UTC consistency regardless of server timezone", () => {
      // Save original timezone
      const originalTZ = process.env.TZ;

      try {
        // Test with different timezones
        const timezones = [
          "UTC",
          "America/New_York",
          "Europe/London",
          "Asia/Tokyo",
        ];
        const results = [];

        timezones.forEach((tz) => {
          process.env.TZ = tz;

          // Force timezone change (this is a test-only approach)
          const testDate = now();
          const isoString = formatForAPI(testDate);

          results.push({
            timezone: tz,
            isoString,
            endsWithZ: isoString.endsWith("Z"),
          });
        });

        // All results should end with 'Z' (UTC indicator)
        results.forEach((result) => {
          expect(result.endsWithZ).toBe(true);
        });

        // All ISO strings should be in the same format
        const formats = results.map((r) =>
          r.isoString.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/)
        );
        formats.forEach((format) => {
          expect(format).toBeTruthy();
        });
      } finally {
        // Restore original timezone
        process.env.TZ = originalTZ;
      }
    });

    test("should handle daylight saving time transitions correctly", () => {
      // Test dates around DST transitions
      const beforeDST = createUTCDateTime("2024-03-09", "12:00"); // Before DST in US
      const afterDST = createUTCDateTime("2024-03-11", "12:00"); // After DST in US

      const difference = getTimeDifference(afterDST, beforeDST, "hours");
      expect(difference).toBe(48); // Should be exactly 48 hours regardless of DST
    });

    test("should handle leap year correctly", () => {
      const feb28_2024 = createUTCDateTime("2024-02-28", "23:59");
      const feb29_2024 = createUTCDateTime("2024-02-29", "00:00");
      const mar01_2024 = createUTCDateTime("2024-03-01", "00:00");

      expect(getTimeDifference(feb29_2024, feb28_2024, "minutes")).toBe(1);
      expect(getTimeDifference(mar01_2024, feb29_2024, "hours")).toBe(24);
    });
  });

  describe("Edge Cases and Error Handling", () => {
    test("should handle invalid dates gracefully", () => {
      expect(formatForAPI(new Date("invalid"))).toBe(null);
      expect(() => addTime(new Date("invalid"), 1, "hours")).toThrow();
      expect(() =>
        getTimeDifference(new Date("invalid"), now(), "hours")
      ).toThrow();
    });

    test("should handle extreme dates", () => {
      const veryOldDate = new Date("1900-01-01T00:00:00.000Z");
      const veryFutureDate = new Date("2100-12-31T23:59:59.999Z");

      expect(formatForAPI(veryOldDate)).toBe("1900-01-01T00:00:00.000Z");
      expect(formatForAPI(veryFutureDate)).toBe("2100-12-31T23:59:59.999Z");

      const difference = getTimeDifference(veryFutureDate, veryOldDate, "days");
      expect(difference).toBeGreaterThan(70000); // More than 200 years
    });

    test("should handle boundary conditions", () => {
      const endOfYear = createUTCDateTime("2024-12-31", "23:59");
      const startOfNextYear = addTime(endOfYear, 1, "minutes");

      expect(startOfNextYear.getUTCFullYear()).toBe(2025);
      expect(startOfNextYear.getUTCMonth()).toBe(0); // January
      expect(startOfNextYear.getUTCDate()).toBe(1);
      expect(startOfNextYear.getUTCHours()).toBe(0);
      expect(startOfNextYear.getUTCMinutes()).toBe(0);
    });
  });

  describe("Performance Tests", () => {
    test("should handle large numbers of operations efficiently", () => {
      const startTime = Date.now();
      const baseDate = now();

      // Perform 1000 operations
      for (let i = 0; i < 1000; i++) {
        const futureDate = addTime(baseDate, i, "minutes");
        const formatted = formatForAPI(futureDate);
        const parsed = new Date(formatted);
        expect(parsed).toBeInstanceOf(Date);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (less than 1 second)
      expect(duration).toBeLessThan(1000);
    });
  });
});

describe("Integration Tests", () => {
  describe("API Response Formatting", () => {
    test("should format nested date objects correctly", () => {
      const testData = {
        user: {
          createdAt: new Date("2024-01-15T14:30:45.123Z"),
          lastOnlineTime: new Date("2024-01-15T15:45:30.456Z"),
          profile: {
            updatedAt: new Date("2024-01-15T16:20:15.789Z"),
          },
        },
        invitations: [
          {
            date: new Date("2024-01-20T10:00:00.000Z"),
            createdAt: new Date("2024-01-15T12:00:00.000Z"),
            users: [
              {
                acceptedAt: new Date("2024-01-15T13:30:00.000Z"),
              },
            ],
          },
        ],
      };

      // This would be handled by the formatTimeResponse middleware
      const {
        formatDatesInObject,
      } = require("../middleware/formatTimeResponse");
      const formatted = formatDatesInObject(testData);

      expect(formatted.user.createdAt).toBe("2024-01-15T14:30:45.123Z");
      expect(formatted.user.lastOnlineTime).toBe("2024-01-15T15:45:30.456Z");
      expect(formatted.user.profile.updatedAt).toBe("2024-01-15T16:20:15.789Z");
      expect(formatted.invitations[0].date).toBe("2024-01-20T10:00:00.000Z");
      expect(formatted.invitations[0].createdAt).toBe(
        "2024-01-15T12:00:00.000Z"
      );
      expect(formatted.invitations[0].users[0].acceptedAt).toBe(
        "2024-01-15T13:30:00.000Z"
      );
    });
  });

  describe("Middleware Tests", () => {
    test("formatTimeResponse middleware should format dates in responses", (done) => {
      const {
        formatTimeResponse,
      } = require("../middleware/formatTimeResponse");

      // Mock request and response objects
      const req = {};
      const res = {
        json: jest.fn(),
      };

      // Apply middleware
      formatTimeResponse(req, res, () => {
        // Test data with dates
        const responseData = {
          success: true,
          data: {
            createdAt: new Date("2024-01-15T14:30:45.123Z"),
            updatedAt: new Date("2024-01-15T15:45:30.456Z"),
          },
        };

        // Call the modified res.json
        res.json(responseData);

        // Verify that res.json was called with formatted dates
        expect(res.json).toHaveBeenCalledWith({
          success: true,
          data: {
            createdAt: "2024-01-15T14:30:45.123Z",
            updatedAt: "2024-01-15T15:45:30.456Z",
          },
        });

        done();
      });
    });
  });

  describe("Database Consistency", () => {
    test("should maintain UTC consistency in database operations", () => {
      // Simulate database save/retrieve cycle
      const originalDate = now();
      const isoString = formatForAPI(originalDate);

      // Simulate storing in database (as ISO string) and retrieving
      const retrievedDate = new Date(isoString);

      expect(retrievedDate.getTime()).toBe(originalDate.getTime());
      expect(formatForAPI(retrievedDate)).toBe(isoString);
    });
  });
});
