const mongoose = require('mongoose');
const Chat = require('../models/Common/Chat');
const Invitation = require('../models/User/invitations');

/**
 * Migration script to fix the group invitation system
 * 
 * This script:
 * 1. Adds acceptedAt timestamps to existing accepted invitations
 * 2. Updates chat participants structure to include userId and joinedAt
 * 3. Removes pending users from chat participants
 */

async function migrateGroupInvitationSystem() {
  try {
    console.log('Starting group invitation system migration...');

    // Step 1: Add acceptedAt timestamps to existing accepted invitations
    console.log('Step 1: Adding acceptedAt timestamps to accepted invitations...');
    
    const acceptedInvitations = await Invitation.find({
      'users.status': 'Accepted',
      'users.acceptedAt': { $exists: false }
    });

    let invitationUpdateCount = 0;
    for (const invitation of acceptedInvitations) {
      let hasUpdates = false;
      
      invitation.users.forEach(user => {
        if (user.status === 'Accepted' && !user.acceptedAt) {
          // Use the invitation's updatedAt as a fallback for acceptedAt
          user.acceptedAt = invitation.updatedAt || invitation.createdAt || new Date();
          hasUpdates = true;
        }
      });

      if (hasUpdates) {
        await invitation.save();
        invitationUpdateCount++;
      }
    }
    
    console.log(`Updated ${invitationUpdateCount} invitations with acceptedAt timestamps`);

    // Step 2: Update chat participants structure
    console.log('Step 2: Updating chat participants structure...');
    
    const chats = await Chat.find({}).populate('invitationId');
    let chatUpdateCount = 0;

    for (const chat of chats) {
      if (!chat.invitationId) {
        console.log(`Skipping chat ${chat._id} - no invitation found`);
        continue;
      }

      // Check if participants are already in new format
      const hasNewFormat = chat.participants.some(p => 
        typeof p === 'object' && p.userId !== undefined
      );

      if (hasNewFormat) {
        console.log(`Chat ${chat._id} already has new participant format`);
        continue;
      }

      // Convert participants to new format
      const newParticipants = [];
      
      // Add invitation creator
      const creatorId = chat.invitationId.invitationBy;
      if (creatorId) {
        newParticipants.push({
          userId: creatorId,
          joinedAt: chat.invitationId.createdAt || chat.createdAt || new Date()
        });
      }

      // Add only accepted users
      if (chat.invitationId.users) {
        chat.invitationId.users.forEach(user => {
          if (user.status === 'Accepted') {
            // Don't add creator twice
            if (user.userId.toString() !== creatorId.toString()) {
              newParticipants.push({
                userId: user.userId,
                joinedAt: user.acceptedAt || chat.invitationId.updatedAt || chat.createdAt || new Date()
              });
            }
          }
        });
      }

      // Update chat with new participants structure
      chat.participants = newParticipants;
      await chat.save();
      chatUpdateCount++;
    }

    console.log(`Updated ${chatUpdateCount} chats with new participant structure`);

    // Step 3: Clean up any orphaned chats (chats with no participants)
    console.log('Step 3: Cleaning up orphaned chats...');
    
    const orphanedChats = await Chat.find({
      $or: [
        { participants: { $size: 0 } },
        { participants: { $exists: false } }
      ]
    });

    let orphanedCount = 0;
    for (const orphanedChat of orphanedChats) {
      console.log(`Marking orphaned chat ${orphanedChat._id} as deleted`);
      orphanedChat.deleted = true;
      await orphanedChat.save();
      orphanedCount++;
    }

    console.log(`Marked ${orphanedCount} orphaned chats as deleted`);

    console.log('Migration completed successfully!');
    console.log(`Summary:
    - Updated ${invitationUpdateCount} invitations with acceptedAt timestamps
    - Updated ${chatUpdateCount} chats with new participant structure
    - Marked ${orphanedCount} orphaned chats as deleted`);

  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Function to rollback the migration (if needed)
async function rollbackMigration() {
  try {
    console.log('Starting rollback of group invitation system migration...');

    // Step 1: Remove acceptedAt fields from invitations
    console.log('Step 1: Removing acceptedAt fields from invitations...');
    
    const result1 = await Invitation.updateMany(
      { 'users.acceptedAt': { $exists: true } },
      { $unset: { 'users.$[].acceptedAt': '' } }
    );
    
    console.log(`Removed acceptedAt from ${result1.modifiedCount} invitations`);

    // Step 2: Convert chat participants back to old format
    console.log('Step 2: Converting chat participants back to old format...');
    
    const chats = await Chat.find({
      'participants.userId': { $exists: true }
    });

    let chatRollbackCount = 0;
    for (const chat of chats) {
      const oldParticipants = chat.participants.map(p => 
        p.userId || p
      );
      
      chat.participants = oldParticipants;
      await chat.save();
      chatRollbackCount++;
    }

    console.log(`Rolled back ${chatRollbackCount} chats to old participant format`);

    console.log('Rollback completed successfully!');

  } catch (error) {
    console.error('Rollback failed:', error);
    throw error;
  }
}

module.exports = {
  migrateGroupInvitationSystem,
  rollbackMigration
};

// If this script is run directly
if (require.main === module) {
  const action = process.argv[2];
  
  if (action === 'rollback') {
    rollbackMigration()
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
  } else {
    migrateGroupInvitationSystem()
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
  }
}
