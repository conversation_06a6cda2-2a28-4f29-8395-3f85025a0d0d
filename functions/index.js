const admin = require('firebase-admin');
const functions = require('firebase-functions');
const chatRules = require('./chatRules');

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  // You can use the default credential or specify a service account
  admin.initializeApp();
}

// Export all functions
exports.validateChatMessage = chatRules.validateChatMessage;
exports.manageChatTimeWindow = chatRules.manageChatTimeWindow;
exports.sendChatNotification = chatRules.sendChatNotification;
