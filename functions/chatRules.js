const functions = require("firebase-functions");
const admin = require("firebase-admin");
const moment = require("moment-timezone");

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

/**
 * Cloud Function that validates chat messages based on user roles and meeting time
 * Triggers on new message creation in Firestore
 */
exports.validateChatMessage = functions.firestore
  .document("messages/{messageId}")
  .onCreate(async (snapshot, context) => {
    try {
      const messageData = snapshot.data();
      const { chatId, senderId, content, timestamp } = messageData;

      // Get chat data
      const chatDoc = await db.collection("chats").doc(chatId).get();
      if (!chatDoc.exists) {
        console.error(`Chat ${chatId} not found`);
        await snapshot.ref.delete();
        return { error: "Chat not found" };
      }

      const chatData = chatDoc.data();
      const { participants, chatOpenTime, chatCloseTime, isGroupChat } =
        chatData;

      // Check if sender is a participant
      if (!participants.includes(senderId)) {
        console.error(
          `User ${senderId} is not a participant in chat ${chatId}`
        );
        await snapshot.ref.delete();
        return { error: "User is not a participant in this chat" };
      }

      // Get sender's user data
      const senderDoc = await db.collection("users").doc(senderId).get();
      if (!senderDoc.exists) {
        console.error(`Sender ${senderId} not found`);
        await snapshot.ref.delete();
        return { error: "Sender not found" };
      }

      const senderData = senderDoc.data();
      const isPremiumSender = senderData.isPremium || false;

      const now = timestamp.toDate();
      const chatOpenTimeDate = chatOpenTime.toDate();
      const chatCloseTimeDate = chatCloseTime.toDate();

      // Check if chat is within time window
      if (now < chatOpenTimeDate) {
        // Chat not open yet
        if (isPremiumSender) {
          // Premium users can send messages early, but we need to update the chat data
          // to reflect that a premium user has started the chat early
          if (!chatData.premiumEarlyStart) {
            await db.collection("chats").doc(chatId).update({
              premiumEarlyStart: true,
              updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            });

            // Notify standard users that a premium user has started the chat early
            const standardUserIds = participants.filter((p) => p !== senderId);
            if (standardUserIds.length > 0) {
              const standardUserDocs = await db
                .collection("users")
                .where(
                  admin.firestore.FieldPath.documentId(),
                  "in",
                  standardUserIds
                )
                .where("isPremium", "==", false)
                .get();

              const tokens = [];
              standardUserDocs.forEach((doc) => {
                const userData = doc.data();
                if (userData.fcmToken) {
                  tokens.push(userData.fcmToken);
                }
              });

              if (tokens.length > 0) {
                await admin.messaging().sendMulticast({
                  notification: {
                    title: "NETME Chat",
                    body: "You have received a new message.",
                  },
                  tokens: tokens,
                });
              }
            }
          }
        } else {
          // Standard user trying to send message before chat is open
          if (!chatData.premiumEarlyStart) {
            // No premium user has started the chat early
            console.error(
              `Chat ${chatId} not open yet for standard user ${senderId}`
            );
            await snapshot.ref.delete();
            return {
              error: "Chat not open yet",
              openTime: chatOpenTimeDate,
              isPremium: isPremiumSender,
            };
          } else {
            // A premium user has started the chat early
            // Check if standard user has already sent 2 messages
            const userMessagesQuery = await db
              .collection("messages")
              .where("chatId", "==", chatId)
              .where("senderId", "==", senderId)
              .get();

            if (userMessagesQuery.size > 2) {
              console.error(
                `Message limit reached for standard user ${senderId} in chat ${chatId}`
              );
              await snapshot.ref.delete();
              return {
                error:
                  "Message limit reached. Upgrade to Premium for unlimited messages.",
                isPremium: isPremiumSender,
              };
            }
          }
        }
      } else if (now > chatCloseTimeDate) {
        // Chat closed
        console.error(`Chat ${chatId} is closed`);
        await snapshot.ref.delete();
        return {
          error: "Chat is closed",
          closeTime: chatCloseTimeDate,
        };
      }

      // If it's a group chat, check if it has at least two participants who accepted
      if (isGroupChat) {
        // For group chats, we need to check if at least two people have accepted
        const meetingDoc = await db
          .collection("meetings")
          .doc(chatData.invitationId)
          .get();
        if (!meetingDoc.exists) {
          console.error(`Meeting ${chatData.invitationId} not found`);
          await snapshot.ref.delete();
          return { error: "Meeting not found" };
        }

        const meetingData = meetingDoc.data();
        const acceptedParticipants = meetingData.participants.filter(
          (p) => p.status === "accepted"
        );
        if (acceptedParticipants.length < 2) {
          console.error(
            `Group chat ${chatId} requires at least two accepted participants`
          );
          await snapshot.ref.delete();
          return {
            error: "Group chat requires at least two accepted participants",
            acceptedCount: acceptedParticipants.length,
          };
        }
      }

      // Message is valid, update read status
      const readData = participants.map((p) => ({
        userId: p,
        readAt:
          p === senderId ? admin.firestore.FieldValue.serverTimestamp() : null,
      }));

      await snapshot.ref.update({ read: readData });

      // Update chat's last message
      await db
        .collection("chats")
        .doc(chatId)
        .update({
          lastMessage: {
            content,
            senderId,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
          },
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

      // Send push notification to other participants
      const otherParticipants = participants.filter((p) => p !== senderId);
      if (otherParticipants.length > 0) {
        const userDocs = await db
          .collection("users")
          .where(
            admin.firestore.FieldPath.documentId(),
            "in",
            otherParticipants
          )
          .get();

        const tokens = [];
        let senderName = "Someone";

        // Get sender name
        const senderNameDoc = await db.collection("users").doc(senderId).get();
        if (senderNameDoc.exists) {
          senderName = senderNameDoc.data().name || "Someone";
        }

        userDocs.forEach((doc) => {
          const userData = doc.data();
          if (userData.fcmToken) {
            tokens.push(userData.fcmToken);
          }
        });

        if (tokens.length > 0) {
          await admin.messaging().sendMulticast({
            notification: {
              title: "NETME Chat",
              body: `${senderName} sent you a message`,
            },
            tokens: tokens,
          });
        }
      }

      return { success: true };
    } catch (error) {
      console.error("Error validating chat message:", error);
      return { error: "Internal server error" };
    }
  });

/**
 * Cloud Function that manages chat time windows
 * Triggers on chat document creation or update
 */
exports.manageChatTimeWindow = functions.firestore
  .document("chats/{chatId}")
  .onWrite(async (change, context) => {
    try {
      // Skip if document is deleted
      if (!change.after.exists) {
        return null;
      }

      const chatData = change.after.data();
      const { participants, chatOpenTime, chatCloseTime } = chatData;

      // Skip if this is an update that doesn't affect time windows
      if (change.before.exists) {
        const beforeData = change.before.data();
        if (
          chatOpenTime.isEqual(beforeData.chatOpenTime) &&
          chatCloseTime.isEqual(beforeData.chatCloseTime)
        ) {
          return null;
        }
      }

      // Schedule notifications for chat open/close
      const now = new Date();

      // Get user FCM tokens
      const userDocs = await db
        .collection("users")
        .where(admin.firestore.FieldPath.documentId(), "in", participants)
        .get();

      const standardUsers = [];
      const premiumUsers = [];

      userDocs.forEach((doc) => {
        const userData = doc.data();
        if (userData.fcmToken) {
          if (userData.isPremium) {
            premiumUsers.push({
              id: doc.id,
              token: userData.fcmToken,
            });
          } else {
            standardUsers.push({
              id: doc.id,
              token: userData.fcmToken,
            });
          }
        }
      });

      // Schedule open notifications
      const chatOpenTimeDate = chatOpenTime.toDate();
      if (chatOpenTimeDate > now) {
        // For premium users
        if (premiumUsers.length > 0) {
          const tokens = premiumUsers.map((u) => u.token);
          const message =
            "Your meetup is in 24 hours – the chat is now open for any last-minute coordination.";

          // Schedule using Cloud Pub/Sub
          const openTime = chatOpenTimeDate.getTime();
          const topic = `chat-open-${context.params.chatId}-premium`;

          await admin.pubsub().topic(topic).publishJSON(
            {
              tokens,
              message,
              chatId: context.params.chatId,
            },
            { scheduleTime: openTime }
          );
        }

        // For standard users
        if (standardUsers.length > 0) {
          const tokens = standardUsers.map((u) => u.token);
          const message =
            "Your meetup is in 3 hours – the chat is now open for any last-minute coordination.";

          // Standard users get notification 3 hours before meeting
          const standardOpenTime = new Date(chatOpenTimeDate);
          const topic = `chat-open-${context.params.chatId}-standard`;

          await admin.pubsub().topic(topic).publishJSON(
            {
              tokens,
              message,
              chatId: context.params.chatId,
            },
            { scheduleTime: standardOpenTime.getTime() }
          );
        }
      }

      // Schedule close notification
      const chatCloseTimeDate = chatCloseTime.toDate();
      if (chatCloseTimeDate > now) {
        const allTokens = [...premiumUsers, ...standardUsers].map(
          (u) => u.token
        );
        if (allTokens.length > 0) {
          const message =
            "Your chat window has closed. You can still view messages but cannot send new ones.";

          const topic = `chat-close-${context.params.chatId}`;
          await admin.pubsub().topic(topic).publishJSON(
            {
              tokens: allTokens,
              message,
              chatId: context.params.chatId,
            },
            { scheduleTime: chatCloseTimeDate.getTime() }
          );
        }
      }

      return { success: true };
    } catch (error) {
      console.error("Error managing chat time window:", error);
      return { error: "Internal server error" };
    }
  });

/**
 * Cloud Function that handles chat notifications
 * Triggers on Pub/Sub messages
 */
exports.sendChatNotification = functions.pubsub
  .topic("chat-notifications")
  .onPublish(async (message) => {
    try {
      const { tokens, message: notificationMessage, chatId } = message.json;

      if (!tokens || tokens.length === 0) {
        return { success: true, message: "No tokens to notify" };
      }

      // Send the notification
      await admin.messaging().sendMulticast({
        notification: {
          title: "NETME Chat",
          body: notificationMessage,
        },
        data: {
          chatId,
        },
        tokens,
      });

      return { success: true };
    } catch (error) {
      console.error("Error sending chat notification:", error);
      return { error: "Internal server error" };
    }
  });
