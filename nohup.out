MongooseError: The `uri` parameter to `openUri()` must be a string, got "undefined". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
    at Connection.openUri (/var/www/html/netme_backend/node_modules/mongoose/lib/connection.js:695:11)
    at /var/www/html/netme_backend/node_modules/mongoose/lib/index.js:414:10
    at /var/www/html/netme_backend/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (<anonymous>)
    at promiseOrCallback (/var/www/html/netme_backend/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose._promiseOrCallback (/var/www/html/netme_backend/node_modules/mongoose/lib/index.js:1288:10)
    at Mongoose.connect (/var/www/html/netme_backend/node_modules/mongoose/lib/index.js:413:20)
    at Object.<anonymous> (/var/www/html/netme_backend/server.js:69:4)
    at Module._compile (node:internal/modules/cjs/loader:1256:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1310:10)
(node:1287334) NOTE: We are formalizing our plans to enter AWS SDK for JavaScript (v2) into maintenance mode in 2023.

Please migrate your code to use AWS SDK for JavaScript (v3).
For more information, check the migration guide at https://a.co/7PzMCcy
(Use `node --trace-warnings ...` to show where the warning was created)
