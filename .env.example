NODE_ENV=development
TOKEN_SECRET=
DB_URI=mongodb://localhost:27017/netmedb
PORT=4576
BASIC_AUTH_USERNAME=
BASIC_AUTH_PASSWORD=

# Debug settings
DEBUG_CORS=true  # Set to true to enable CORS debugging
AWS_REGION=
S3_BUCKET_NAME=
S3_BUCKET_NAME_GOOGLE=
ACCESS_KEY_ID=
SECRET_ACCESS_KEY=
AWS_ACCESS_KEY_ID_SES=
AWS_SECRET_ACCESS_KEY_SES=
SES_USER=
SES_PASS=
MAP_KEY=

# Auth Token
JWT_SECRET=
JWT_TOKEN_AGE=
REFRESH_TOKEN_SECRET=
REFRESH_TOKEN_AGE=

STRIPE_SECRET=
STRIPE_PUBLISH_KEY=
STRIPE_WEBHOOK_SECRET=
SITE_SECRET=
OFF_25_COUPON=
OFF_50_COUPON=
OFF_75_COUPON=
BUY_ONE_GET_ONE_FREE=
ONE_MONTH_FREE_COUPON=
THREE_MONTHS_FREE_COUPON=
SIX_MONTHS_FREE_COUPON=
TWELVE_MONTHS_FREE_COUPON=
BC_URI=
SIGNED_URL_EXPIRY_TIME=7200
CANCEL_URL=
CLIENT_TOKEN=