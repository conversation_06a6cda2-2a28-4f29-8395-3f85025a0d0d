const admin = require('firebase-admin');
const userServiceAccount = require('../config/service.json');
const partnerServiceAccount = require('../config/partner_service.json');

// Initialize the default app for users if not already initialized
if (!admin.apps.some(app => app.name === '[DEFAULT]')) {
  admin.initializeApp({
    credential: admin.credential.cert(userServiceAccount),
  });
}

// Initialize a named app for partners if not already initialized
const partnerApp = admin.apps.some(app => app.name === 'Partner') ?
  admin.app('Partner') :
  admin.initializeApp({
    credential: admin.credential.cert(partnerServiceAccount),
  }, 'Partner');

module.exports = {
  userApp: admin.app(),
  partnerApp,
};
