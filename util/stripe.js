const Stripe = require("stripe");
const UserSubscription = require("../models/User/userSubscription");
const User = require("../models/User/User");
const PartnerSubscription = require("../models/Partner/partnerSubscription");
const Partner = require("../models/Partner/Partner");
const Bundle = require("../models/User/bundle");
const { calculateExpiryDate } = require("./calculateExpiryDate");
exports.stripe = new Stripe(process.env.STRIPE_SECRET, {
  apiVersion: "2024-06-20",
  typescript: true,
});
module.exports.createCheckoutLink = (price, metadata = {}, successUrl) => {
  return new Promise(async (resolve, reject) => {
    try {
      console.log(price, metadata);
      const session = await this.stripe.checkout.sessions.create({
        payment_method_types: ["card"],
        line_items: [
          {
            price_data: {
              currency: "eur",
              product_data: {
                name: metadata.planName,
              },
              recurring: {
                interval: "month", // or 'year'
              },
              unit_amount: Math.floor(price * 100),
            },
            quantity: 1,
          },
        ],
        mode: "subscription",
        success_url: successUrl,
        cancel_url: successUrl,
        customer_email: metadata.email,
        metadata,
      });

      resolve(session.url);
    } catch (error) {
      console.log(error);
      reject(error.message);
    }
  });
};

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

module.exports.listenWebhook = async (req, body) => {
  const signature =
    req.headers["Stripe-Signature"] || req.headers["stripe-signature"];

  let event;

  try {
    event = this.stripe.webhooks.constructEvent(body, signature, webhookSecret);
  } catch (error) {
    console.log(`Webhook Error: ${error.message}`);
    return res.status(400).send(`Webhook Error: ${error.message}`);
  }
  // Handle the event
  const now = new Date();
  const session = event.data.object;
  const metdataOut = session.metadata;
  console.log("session", session);
  // if (metdataOut.isUpgrade === "false") {
  switch (event.type) {
    // case "customer.subscription.created":
    //   const customerSubscriptionCreated = event.data.object;
    //   console.log("customerSubscriptionCreated");
    //   try {
    //     // Set trial period for the created subscription
    //     const trialEndDate = Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60;
    //     await this.stripe.subscriptions.update(
    //       customerSubscriptionCreated.id,
    //       {
    //         trial_end: trialEndDate,
    //       }
    //     );
    //     console.log(
    //       `Trial period added to subscription ${customerSubscriptionCreated.id}`
    //     );
    //   } catch (error) {
    //     console.error(`Failed to update subscription: ${error.message}`);
    //     return res.status(400).json({ error: error.message });
    //   }
    //   break;
    case "customer.subscription.updated":
      const customerSubscriptionUpdated = event.data.object;
      if (customerSubscriptionUpdated.metadata.isActive === "false") {
        try {
          // Set trial period for the created subscription
          const trialEndDate = Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60;
          const update = await this.stripe.subscriptions.update(
            customerSubscriptionUpdated.id,
            {
              trial_end: trialEndDate,
            }
          );
          console.log(
            `Trial period added to subscription ${customerSubscriptionUpdated.id}`
          );
        } catch (error) {
          console.error(`Failed to update subscription: ${error.message}`);
          return res.status(400).json({ error: error.message });
        }
      } else if (customerSubscriptionUpdated.metadata.isCancel === "true") {
        console.log("isCancel True");
        const partnerSubscription = await PartnerSubscription.findOne({
          partnerId: customerSubscriptionUpdated.metadata.userId,
        });
        await PartnerSubscription.updateOne(
          { _id: partnerSubscription._id },
          {
            subscriptionId: customerSubscriptionUpdated.metadata.subscriptionId,
            isCancel: true, // Update with the new plan name
          }
        );
      } else {
        const partnerSubscription = await PartnerSubscription.findOne({
          partnerId: customerSubscriptionUpdated.metadata.userId,
        });
        console.log(
          "customerSubscriptionUpdated",
          customerSubscriptionUpdated.metadata
        );
        await PartnerSubscription.updateOne(
          { _id: partnerSubscription._id },
          {
            subscriptionId: customerSubscriptionUpdated.metadata.subscriptionId,
            isCancel: false, // Update with the new plan name
            isActive: true,
          }
        );
      }
      break;
    case "checkout.session.completed": {
      const checkoutSessionCompleted = event.data.object;
      console.log("checkoutCompleated", checkoutSessionCompleted);
      const metdata = checkoutSessionCompleted.metadata;
      console.log("checkout.session.completed.metdata", metdata);
      const userId = metdata.userId;

      if (metdata.userType === "PARTNER") {
        const expireAt = calculateExpiryDate(Number(metdata.month));
        console.log("metdata.month", metdata.month);
        const partnerSubscriptionExist = await PartnerSubscription.findOne({
          partnerId: metdata.userId,
        });
        console.log("partnerSubscriptionExist", partnerSubscriptionExist);
        if (!!partnerSubscriptionExist) {
          console.log("event.data.object.id", event.data.object.id);
          await PartnerSubscription.findOneAndUpdate(
            { partnerId: metdata.userId },
            {
              subscriptionId: metdata.subscriptionId,
              isCancel: false,
              expireAt,
              usedVoucher: metdata.usedVoucher,
              timePeriod: Number(metdata.month),
              stripeSubscriptionId: event.data.object.subscription,
              stripePaymentId: event.data.object.payment_intent,
              isActive: true,
            }
          );
          await Partner.findByIdAndUpdate(
            { _id: metdata.userId },
            { isPremium: true },
            { new: true }
          );
        }
        //  else {
        //   console.log("event.data.object.id", event.data.object.id);
        //   const partSubNew = await PartnerSubscription.create({
        //     partnerId: metdata.userId,
        //     expireAt,
        //     usedVoucher: metdata.usedVoucher,
        //     isActive: false,
        //     timePeriod: Number(metdata.month),
        //     stripeSubscriptionId: event.data.object.subscription,
        //     stripePaymentId: event.data.object.payment_intent,
        //     subscriptionId: metdata.subscriptionId,
        //   });
        //   console.log("partSubNew", partSubNew);
        // }
        return;
      }
      break;
    }
    // case "payment_intent.succeeded": {
    //   const paymentIntentSucceeded = event.data.object;
    //   const partnerSubscriptionExist = await PartnerSubscription.findOne({
    //     partnerId: paymentIntentSucceeded.metadata.userId,
    //   });
    //   if (partnerSubscriptionExist) {
    //     await PartnerSubscription.findByIdAndUpdate(
    //       { _id: partnerSubscriptionExist._id },
    //       { stripePaymentId: invoicePaymentSucceeded.payment_intent },
    //       { new: true }
    //     );
    //   }
    // }
    case "invoice.payment_succeeded": {
      const invoicePaymentSucceeded = event.data.object;
      console.log("invoicePaymentSucceeded", invoicePaymentSucceeded);
      const partnerSubscriptionExist = await PartnerSubscription.findOne({
        partnerId: invoicePaymentSucceeded.subscription_details.metadata.userId,
      });
      console.log("partnerSubscriptionExist", partnerSubscriptionExist);
      if (
        partnerSubscriptionExist &&
        !partnerSubscriptionExist.stripePaymentId
      ) {
        await PartnerSubscription.findByIdAndUpdate(
          { _id: partnerSubscriptionExist._id },
          { stripePaymentId: invoicePaymentSucceeded.payment_intent },
          { new: true }
        );
      }
      const subscriptionId = invoicePaymentSucceeded.subscription;

      // Retrieve the subscription metadata
      const subscription = await this.stripe.subscriptions.retrieve(
        subscriptionId
      );
      console.log("subscription", subscription);
      if (subscription.metadata.hasFreeMonth === "true") {
        // Apply a 100% discount for the next month
        await this.stripe.subscriptions.update(subscriptionId, {
          metadata: { ...subscription.metadata, hasFreeMonth: "false" },
          discounts: [{ coupon: process.env.ONE_MONTH_FREE_COUPON }],
        });

        console.log(`Free month applied for subscription ${subscriptionId}`);
      }
      break;
    }
    default:
  }
  // }

  // if (event.type === "checkout.session.completed") {
  //   const session = event.data.object;
  //   const metaData = session.metadata;
  //   console.log("event", event);
  //   console.log("metaData", metaData);
  //   if (metaData.purchaseType === "BUNDLE") {
  //     await Bundle.findOneAndUpdate(
  //       { userId: metaData.userId },
  //       { $inc: { pendingInvitation: Number(metaData.totalInvitations) } },
  //       { new: true, upsert: true }
  //     );
  //   }
  //   if (metaData.purchaseType === "SUBSCRIPTION") {
  //     if (metaData.userType === "USER") {
  //       const expireAt = calculateExpiryDate(metaData.month);
  //       await UserSubscription.updateMany(
  //         { userId: metaData.userId },
  //         { isActive: false }
  //       );
  //       await UserSubscription.create({
  //         userId: metaData.userId,
  //         expireAt,
  //         isActive: true,
  //         subscriptionId: metaData.subscriptionId,
  //       });
  //       await User.findByIdAndUpdate(
  //         { _id: metaData.userId },
  //         { isPremium: true },
  //         { new: true }
  //       );
  //       return;
  //     }
  //     if (metaData.userType === "PARTNER") {
  //       const expireAt = calculateExpiryDate(Number(metaData.month));
  //       console.log("metaData.month", metaData.month);
  //       const partnerSubscriptionExist = await PartnerSubscription.findOne({
  //         partnerId: metaData.userId,
  //       });
  //       console.log("partnerSubscriptionExist", partnerSubscriptionExist);
  //       if (!!partnerSubscriptionExist) {
  //         await PartnerSubscription.updateMany(
  //           { partnerId: metaData.userId },
  //           {
  //             subscriptionId: metaData.subscriptionId,
  //             isCancel: false,
  //             expireAt,
  //             usedVoucher: "",
  //             timePeriod: Number(metaData.month),
  //             stripeSubscriptionId: event.data.object.subscription,
  //             stripePaymentId: event.data.object.payment_intent,
  //             isActive: true,
  //           }
  //         );
  //       } else {
  //         await PartnerSubscription.create({
  //           partnerId: metaData.userId,
  //           expireAt,
  //           usedVoucher: metaData.usedVoucher,
  //           isActive: false,
  //           timePeriod: Number(metaData.month),
  //           stripeSubscriptionId: event.data.object.subscription,
  //           stripePaymentId: event.data.object.payment_intent,
  //           subscriptionId: metaData.subscriptionId,
  //         });
  //       }

  //       await Partner.findByIdAndUpdate(
  //         { _id: metaData.userId },
  //         { isPremium: true },
  //         { new: true }
  //       );
  //       return;
  //     }
  //   }
  // }
};
