// const UserSubscription = require("../models/User/userSubscription");
// const User = require("../models/User/User");
// module.exports.checkUserIsPremium = async (userId) => {
//   const findUser = await User.findById(userId);
//   const memberShip = await UserSubscription.findOne({ isActive: true, userId });
//   if (findUser && memberShip) {
//     return true;
//   } else false;
// };

const User = require("../models/User/User");

module.exports.checkUserIsPremium = async (userId) => {
  try {
    // Fetch the user in a single query
    const user = await User.findOne({ _id: userId, isPremium: true });

    // If the user exists and isPremium is true, return true; otherwise, return false
    return !!user; // Convert user object to boolean
  } catch (error) {
    console.error("Error checking user premium status:", error);
    return false; // Return false in case of an error
  }
};
