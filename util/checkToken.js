const CLIENT_TOKEN_SECRET = process.env.CLIENT_TOKEN;

// Middleware function to check if the token matches the secret
module.exports.checkToken = (req, res, next) => {
  const token = req.headers.authorization; // The token should be passed in the 'Authorization' header
  console.log("Received Token:", token);
  console.log("CLIENT_TOKEN_SECRET:", CLIENT_TOKEN_SECRET);

  // Check if the token exists
  if (!token) {
    return res
      .status(401)
      .json({ message: "Unauthorized Request - Token Missing" });
  }

  // Remove 'Bearer ' from the token
  const tokenWithoutBearer = token.replace("Bearer ", "");

  // Compare the token with the secret directly
  if (tokenWithoutBearer === CLIENT_TOKEN_SECRET) {
    // If the tokens match, proceed to the next middleware or route handler
    return next();
  } else {
    // If the tokens don't match, return unauthorized response
    return res
      .status(401)
      .json({ message: "Unauthorized Request - Token Mismatch" });
  }
};
