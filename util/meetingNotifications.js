const { sendEmail } = require("./sendEmail");

/**
 * Send a warning email to a user who has missed 3 meetings
 * @param {Object} user - The user object containing email and name information
 */
const sendMissedMeetingsWarningEmail = async (user) => {
  try {
    const mailOptions = {
      from: '"NETME App" <' + process.env.USER_APP_EMAIL + ">",
      to: user.email,
      subject: "Warning: Multiple Missed Meetings",
      text: `
        <div style="font-family: Arial, sans-serif; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="color: #e74c3c;">⚠️ Warning: Multiple Missed Meetings ⚠️</h2>
          </div>
          
          <p>Dear ${
            user.userName || user.fullName || user.firstName || "Valued User"
          },</p>
          
          <p>We've noticed that you've missed 3 scheduled meetings on NETME. This is a friendly reminder that consistent attendance is important for maintaining a positive experience for all users on our platform.</p>
          
          <p><strong>Important:</strong> If you miss one more meeting, your account may be automatically deactivated according to our community guidelines.</p>
          
          <p>If you're having trouble with the app or there are circumstances preventing you from attending your scheduled meetings, please contact our support team. We're here to help!</p>
          
          <p>Tips to avoid missing meetings:</p>
          <ul>
            <li>Enable notifications for meeting reminders</li>
            <li>Add meetings to your calendar</li>
            <li>If you can't attend, reschedule or cancel in advance</li>
          </ul>
          
          <p>Thank you for your understanding and cooperation.</p>
          
          <p>Best regards,<br>
          The NETME Team</p>
          
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #777; text-align: center;">
            <p>This is an automated message. Please do not reply directly to this email.</p>
          </div>
        </div>
      `,
    };

    await sendEmail(mailOptions);
    console.log(
      `Warning email sent to user ${user._id} (${user.email}) for missing 3 meetings`
    );
    return true;
  } catch (error) {
    console.error("Error sending missed meetings warning email:", error);
    return false;
  }
};

/**
 * Send an account deletion notification email to a user who has missed more than 4 meetings
 * @param {Object} user - The user object containing email and name information
 */
const sendAccountDeletionEmail = async (user) => {
  try {
    const mailOptions = {
      from: '"NETME App" <' + process.env.USER_APP_EMAIL + ">",
      to: user.email,
      subject: "Account Deactivation Notice",
      text: `
        <div style="font-family: Arial, sans-serif; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="color: #e74c3c;">Account Deactivation Notice</h2>
          </div>
          
          <p>Dear ${
            user.userName || user.fullName || user.firstName || "Valued User"
          },</p>
          
          <p>We regret to inform you that your NETME account has been deactivated due to missing more than 4 scheduled meetings.</p>
          
          <p>As outlined in our community guidelines, consistent attendance at scheduled meetings is essential to maintain a positive experience for all users on our platform.</p>
          
          <p>If you believe this decision was made in error or would like to appeal, please contact our support team within the next 30 days. After this period, your account data may be permanently deleted in accordance with our data retention policies.</p>
          
          <p>We value your participation and hope to see you back on NETME in the future.</p>
          
          <p>Best regards,<br>
          The NETME Team</p>
          
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #777; text-align: center;">
            <p>This is an automated message. Please do not reply directly to this email.</p>
          </div>
        </div>
      `,
    };

    await sendEmail(mailOptions);
    console.log(
      `Account deletion email sent to user ${user._id} (${user.email})`
    );
    return true;
  } catch (error) {
    console.error("Error sending account deletion email:", error);
    return false;
  }
};

module.exports = {
  sendMissedMeetingsWarningEmail,
  sendAccountDeletionEmail,
};
