// const getReqHelper = (query) => {
//     let filters = { ...query };

//     // if (query.search) {
//     //     filters = {};
//     //     const searchQuery = query.search;
//     //     filters.$or = [
//     //         { lead_Id: { $regex: searchQuery, $options: 'i' } }, // Case-insensitive search for lead_id
//     //         { full_name: { $regex: searchQuery, $options: 'i' } } // Case-insensitive search for name
//     //     ];
//     // } else {
//     //     filters = { ...query }
//     // }

//     const excludeField = ['sort', 'page', 'limit'];
//     excludeField.forEach(field => delete query[field]);


//     const queries = {};

//     let filtersString = JSON.stringify(filters);
//     filtersString = filtersString.replace(/\b(gt|gte|lt|lte)\b/g, (match) => `$${match}`);
//     filters = JSON.parse(filtersString);

//     if (query.sort) {
//         const sortBy = query.sort.split(',').join(' ');
//         queries.sortBy = sortBy;
//     }

//     if (query.fields) {
//         const fields = query.fields.split(',').join(' ');
//         queries.fields = fields;
//     }

//     let page = 1;
//     let limit = 10;

//     if (query.page) {
//         page = query.page;
//     }

//     if (query.limit) {
//         limit = query.limit;
//     }

//     const skip = (page - 1) * parseInt(limit);
//     queries.skip = skip;
//     queries.limit = parseInt(limit);

//     return { filters, queries, page };
// };

// module.exports = getReqHelper;


const getReqHelper = (query) => {
    let filters = { ...query };
    if (query.search) {
        filters = {};
        const searchQuery = query.search;
        filters.$or = [
            { lead_Id: { $regex: searchQuery, $options: 'i' } },
            { full_name: { $regex: searchQuery, $options: 'i' } },
            { company_name: { $regex: searchQuery, $options: 'i' } },
            { email: { $regex: searchQuery, $options: 'i' } },
            { report_name: { $regex: searchQuery, $options: 'i' } },
            { phone: { $regex: searchQuery, $options: 'i' } },
        ];
    } else {
        filters = { ...query }
    }

    // sort, page, limit => exclude
    const excludeField = ['sort', 'page', 'limit'];
    excludeField.forEach(field => delete filters[field])
    const queries = {};

    // gt, gte, lte, lt
    let filtersString = JSON.stringify(filters);
    filtersString = filtersString.replace(/\b(gt|gte|lt|lte)\b/g, match => `$${match}`)
    filters = JSON.parse(filtersString)
    if (query.sort) {
        const sortBy = query.sort.split(",").join(" ")
        queries.sortBy = sortBy
    }
    if (query.fields) {
        const fields = query.fields.split(",").join(" ")
        queries.fields = fields
    }
    let page = 1;
    let limit = 10;
    if (query.page) {
        page = query.page;
    }
    if (query.limit) {
        limit = query.limit;
    }
    const skip = (page - 1) * parseInt(limit);
    queries.skip = skip;
    queries.limit = parseInt(limit);
    const statusArray = filters.status ? filters.status.split(',') : [];
    const serviceArray = filters.service_type ? filters.service_type.split(',') : [];
    const countryArray = filters.country ? filters.country.split(',') : [];
    const regionArray = filters.region ? filters.region.split(',') : [];
    const classArray = filters.lead_class ? filters.lead_class.split(',') : [];
    const platformsArray = filters.platform ? filters.platform.split(',') : [];
    const salesArray = filters['sales_by.id'] ? filters['sales_by.id'].split(',') : [];
    const marketingArray = filters['marketed_by.id'] ? filters['marketed_by.id'].split(',') : [];
    if (statusArray.length > 0) {
        filters = { ...filters, status: { $in: statusArray } }
    }
    if (serviceArray.length > 0) {
        filters = { ...filters, service_type: { $in: serviceArray } }
    }
    if (countryArray.length > 0) {
        filters = { ...filters, country: { $in: countryArray } }
    }
    if (regionArray.length > 0) {
        filters = { ...filters, region: { $in: regionArray } }
    }
    if (classArray.length > 0) {
        filters = { ...filters, lead_class: { $in: classArray } }
    }
    if (platformsArray.length > 0) {
        filters = { ...filters, platform: { $in: platformsArray } }
    }
    if (salesArray.length > 0) {
        filters = { ...filters, 'sales_by.id': { $in: salesArray } }
    }
    if (marketingArray.length > 0) {
        filters = { ...filters, 'marketed_by.id': { $in: marketingArray } }
    }
    return { filters, queries, page }

}

module.exports = getReqHelper;