const nodemailer = require("nodemailer");

const { SESClient, SendEmailCommand } = require("@aws-sdk/client-ses");
const sesClient = new SESClient({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID_SES,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY_SES,
  },
});

module.exports.sendEmail = async (mailOptions) => {
  try {
    const params = {
      Source: mailOptions.from,
      Destination: {
        ToAddresses: [mailOptions.to],
      },
      Message: {
        Subject: {
          Data: mailOptions.subject,
        },
        Body: {
          Html: {
            Data: mailOptions.text,
          },
        },
      },
    };

    const data = await sesClient.send(new SendEmailCommand(params));
    console.log("Email sent successfully:", data);
  } catch (error) {
    console.log("Error sending email:", error);
  }
};
