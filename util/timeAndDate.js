const { parseTimeString } = require("./timeUtils");

/**
 * Extract time from ISO string in UTC
 * @param {string} isoString - ISO 8601 string
 * @returns {string} Time in HH:MM format (UTC)
 */
function extractTimeFromISOString(isoString) {
  const date = new Date(isoString);

  // Extract UTC hours, minutes, seconds
  const hours = String(date.getUTCHours()).padStart(2, "0");
  const minutes = String(date.getUTCMinutes()).padStart(2, "0");

  return `${hours}:${minutes}`;
}

/**
 * Parse time parts from various time string formats
 * @param {string} timeStr - Time string
 * @returns {Array|null} [hours, minutes] or null if invalid
 */
function extractTimeParts(timeStr) {
  return parseTimeString(timeStr);
}

module.exports = {
  extractTimeFromISOString,
  extractTimeParts,
};
