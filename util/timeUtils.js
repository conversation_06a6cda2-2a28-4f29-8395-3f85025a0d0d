/**
 * UTC Time Utilities
 * 
 * This module provides standardized UTC time operations to ensure consistent
 * timezone handling across the entire application. All functions work with UTC
 * and return UTC timestamps.
 * 
 * CRITICAL: Always use these utilities instead of direct Date operations
 * to ensure 100% UTC consistency in the database and API responses.
 */

/**
 * Get current UTC timestamp
 * @returns {Date} Current UTC date
 */
const now = () => {
  return new Date(); // JavaScript Date constructor always returns UTC when stored in MongoDB
};

/**
 * Get current UTC timestamp as ISO string
 * @returns {string} Current UTC time in ISO 8601 format
 */
const nowISO = () => {
  return new Date().toISOString();
};

/**
 * Create UTC date from date string and time string
 * @param {string} dateStr - Date in YYYY-MM-DD format
 * @param {string} timeStr - Time in HH:MM format
 * @returns {Date} UTC Date object
 */
const createUTCDateTime = (dateStr, timeStr) => {
  if (!dateStr || !timeStr) {
    throw new Error('Both date and time are required');
  }
  
  // Parse time components
  const timeParts = timeStr.match(/^(\d{1,2}):(\d{2})$/);
  if (!timeParts) {
    throw new Error('Invalid time format. Use HH:MM');
  }
  
  const hours = parseInt(timeParts[1], 10);
  const minutes = parseInt(timeParts[2], 10);
  
  if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
    throw new Error('Invalid time values');
  }
  
  // Create UTC date - this ensures the date is treated as UTC
  const utcDate = new Date(`${dateStr}T${timeStr}:00.000Z`);
  
  if (isNaN(utcDate.getTime())) {
    throw new Error('Invalid date/time combination');
  }
  
  return utcDate;
};

/**
 * Get start of day in UTC
 * @param {Date|string} date - Date object or date string
 * @returns {Date} Start of day in UTC
 */
const getStartOfDayUTC = (date) => {
  const targetDate = typeof date === 'string' ? new Date(date) : new Date(date);
  const utcDate = new Date(targetDate.getUTCFullYear(), targetDate.getUTCMonth(), targetDate.getUTCDate(), 0, 0, 0, 0);
  return utcDate;
};

/**
 * Get end of day in UTC
 * @param {Date|string} date - Date object or date string
 * @returns {Date} End of day in UTC
 */
const getEndOfDayUTC = (date) => {
  const targetDate = typeof date === 'string' ? new Date(date) : new Date(date);
  const utcDate = new Date(targetDate.getUTCFullYear(), targetDate.getUTCMonth(), targetDate.getUTCDate(), 23, 59, 59, 999);
  return utcDate;
};

/**
 * Add time to a date safely in UTC
 * @param {Date} date - Base date
 * @param {number} amount - Amount to add
 * @param {string} unit - Unit: 'minutes', 'hours', 'days', 'months', 'years'
 * @returns {Date} New date with time added
 */
const addTime = (date, amount, unit) => {
  const newDate = new Date(date);
  
  switch (unit) {
    case 'minutes':
      newDate.setUTCMinutes(newDate.getUTCMinutes() + amount);
      break;
    case 'hours':
      newDate.setUTCHours(newDate.getUTCHours() + amount);
      break;
    case 'days':
      newDate.setUTCDate(newDate.getUTCDate() + amount);
      break;
    case 'months':
      newDate.setUTCMonth(newDate.getUTCMonth() + amount);
      break;
    case 'years':
      newDate.setUTCFullYear(newDate.getUTCFullYear() + amount);
      break;
    default:
      throw new Error('Invalid time unit. Use: minutes, hours, days, months, years');
  }
  
  return newDate;
};

/**
 * Subtract time from a date safely in UTC
 * @param {Date} date - Base date
 * @param {number} amount - Amount to subtract
 * @param {string} unit - Unit: 'minutes', 'hours', 'days', 'months', 'years'
 * @returns {Date} New date with time subtracted
 */
const subtractTime = (date, amount, unit) => {
  return addTime(date, -amount, unit);
};

/**
 * Get date range for queries (start and end of day in UTC)
 * @param {Date|string} startDate - Start date
 * @param {Date|string} endDate - End date (optional, defaults to startDate)
 * @returns {Object} Object with startOfDay and endOfDay in UTC
 */
const getDateRangeUTC = (startDate, endDate = null) => {
  const start = getStartOfDayUTC(startDate);
  const end = getEndOfDayUTC(endDate || startDate);
  
  return {
    startOfDay: start,
    endOfDay: end
  };
};

/**
 * Calculate difference between two dates in specified unit
 * @param {Date} date1 - First date
 * @param {Date} date2 - Second date
 * @param {string} unit - Unit: 'milliseconds', 'seconds', 'minutes', 'hours', 'days'
 * @returns {number} Difference in specified unit
 */
const getTimeDifference = (date1, date2, unit = 'milliseconds') => {
  const diffMs = Math.abs(date1.getTime() - date2.getTime());
  
  switch (unit) {
    case 'milliseconds':
      return diffMs;
    case 'seconds':
      return Math.floor(diffMs / 1000);
    case 'minutes':
      return Math.floor(diffMs / (1000 * 60));
    case 'hours':
      return Math.floor(diffMs / (1000 * 60 * 60));
    case 'days':
      return Math.floor(diffMs / (1000 * 60 * 60 * 24));
    default:
      throw new Error('Invalid unit. Use: milliseconds, seconds, minutes, hours, days');
  }
};

/**
 * Check if a date is in the future (compared to current UTC time)
 * @param {Date} date - Date to check
 * @returns {boolean} True if date is in the future
 */
const isFuture = (date) => {
  return date > now();
};

/**
 * Check if a date is in the past (compared to current UTC time)
 * @param {Date} date - Date to check
 * @returns {boolean} True if date is in the past
 */
const isPast = (date) => {
  return date < now();
};

/**
 * Check if a date is today (in UTC)
 * @param {Date} date - Date to check
 * @returns {boolean} True if date is today
 */
const isToday = (date) => {
  const today = now();
  return date.getUTCDate() === today.getUTCDate() &&
         date.getUTCMonth() === today.getUTCMonth() &&
         date.getUTCFullYear() === today.getUTCFullYear();
};

/**
 * Format date for API responses (always ISO 8601 UTC)
 * @param {Date} date - Date to format
 * @returns {string} ISO 8601 UTC string
 */
const formatForAPI = (date) => {
  if (!date) return null;
  return date.toISOString();
};

/**
 * Parse time string and extract hours/minutes
 * @param {string} timeStr - Time string in various formats
 * @returns {Array|null} [hours, minutes] or null if invalid
 */
const parseTimeString = (timeStr) => {
  if (!timeStr) return null;
  
  // Handle different time formats
  let match;
  
  // Format: "HH:MM" or "H:MM"
  match = timeStr.match(/^(\d{1,2}):(\d{2})$/);
  if (match) {
    const hours = parseInt(match[1], 10);
    const minutes = parseInt(match[2], 10);
    if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
      return [hours, minutes];
    }
  }
  
  // Format: "TimeOfDay(HH:MM)"
  match = timeStr.match(/TimeOfDay\((\d{1,2}):(\d{2})\)/);
  if (match) {
    const hours = parseInt(match[1], 10);
    const minutes = parseInt(match[2], 10);
    if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
      return [hours, minutes];
    }
  }
  
  return null;
};

/**
 * Validate timezone string using IANA timezone identifiers
 * @param {string} timezone - Timezone string to validate
 * @returns {boolean} True if valid timezone
 */
const validateTimezone = (timezone) => {
  try {
    Intl.DateTimeFormat(undefined, { timeZone: timezone });
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Create expiry date by adding months to current date
 * @param {number} months - Number of months to add
 * @returns {Date} Expiry date in UTC
 */
const createExpiryDate = (months) => {
  return addTime(now(), months, 'months');
};

/**
 * Safe date operation wrapper
 * @param {Function} operation - Date operation function
 * @returns {*} Result of operation or throws descriptive error
 */
const safeDateOperation = async (operation) => {
  try {
    return await operation();
  } catch (error) {
    if (error.name === 'RangeError') {
      throw new Error('Invalid date operation: ' + error.message);
    }
    throw error;
  }
};

module.exports = {
  // Core functions
  now,
  nowISO,
  createUTCDateTime,
  
  // Date range functions
  getStartOfDayUTC,
  getEndOfDayUTC,
  getDateRangeUTC,
  
  // Date arithmetic
  addTime,
  subtractTime,
  getTimeDifference,
  
  // Date checking
  isFuture,
  isPast,
  isToday,
  
  // Formatting and parsing
  formatForAPI,
  parseTimeString,
  
  // Validation
  validateTimezone,
  
  // Utility functions
  createExpiryDate,
  safeDateOperation
};
