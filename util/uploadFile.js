const fs = require("fs/promises");
const { S3 } = require("aws-sdk");
const path = require("path");
const sharp = require("sharp");
// import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
const s3 = new S3({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.ACCESS_KEY_ID,
  secretAccessKey: process.env.SECRET_ACCESS_KEY,
});

module.exports.uploadAnyFileToS3 = async (file, fileName) => {
  const contentType = `image/${path.extname(fileName).slice(1)}`;
  console.log("contentType", contentType);
  const params = {
    Bucket: process.env.S3_BUCKET_NAME,
    Key: fileName,
    Body: file,
    ContentType: contentType,
  };
  console.log("params", params);
  console.log("file", file);
  console.log("fileName", fileName);
  return new Promise((resolve, reject) => {
    s3.upload(params, function (err, data) {
      if (err) {
        console.log(err);
        reject(err);
      }
      console.log(data);
      resolve(data.Location);
    });
  });
};
module.exports.uploadFileToS3 = async (file, fileName) => {
  try {
    // Get the file extension and determine the content type
    const fileExtension = path.extname(fileName).slice(1).toLowerCase();
    const contentType = `image/${fileExtension}`;

    // Compress and optimize the image using sharp
    const compressedImage = await sharp(file)
      .rotate()
      .resize({
        width: 800, // Set a max width
        //height: 800, // Set a max height to maintain aspect ratio
        fit: "inside", // Fit within the width and height
      })
      .toFormat(fileExtension === "png" ? "png" : "jpeg", {
        quality: 85, // Adjust quality level for better clarity
        progressive: true, // Enable progressive loading for JPEGs
        compressionLevel: 6, // Set compression level for PNGs if needed
      })
      .toBuffer();

    // Prepare the S3 upload parameters
    const params = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: fileName,
      Body: compressedImage,
      ContentType: contentType,
    };

    // Upload the compressed image to S3
    return new Promise((resolve, reject) => {
      s3.upload(params, (err, data) => {
        if (err) {
          console.log("Upload error:", err);
          reject(err);
        } else {
          console.log("Upload success:", data);
          resolve(data.Location);
        }
      });
    });
  } catch (error) {
    console.error("Error compressing or uploading image:", error);
    throw error;
  }
};
module.exports.profilePictureToS3 = async (file, fileName) => {
  try {
    // Get the file extension and determine the content type
    const fileExtension = path.extname(fileName).slice(1).toLowerCase();
    const contentType = `image/png`;

    // Compress and optimize the image using sharp
    const compressedImage = await sharp(file)
      .rotate() // Auto-rotate based on EXIF data to correct orientation
      .resize({
        width: 60, // Max width for profile picture
        height: 60, // Max height to maintain aspect ratio
        fit: "inside", // Fit within the width and height, maintaining aspect ratio
      })
      .png({
        compressionLevel: 6, // Moderate compression for balance between size and quality
        adaptiveFiltering: true, // Further optimizes PNG compression
        palette: true, // Reduces colors if possible to optimize size
      })
      .toBuffer();

    // Prepare the S3 upload parameters
    const params = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: fileName,
      Body: compressedImage,
      ContentType: contentType,
    };

    // Upload the compressed image to S3
    return new Promise((resolve, reject) => {
      s3.upload(params, (err, data) => {
        if (err) {
          console.log("Upload error:", err);
          reject(err);
        } else {
          console.log("Upload success:", data);
          resolve(data.Location);
        }
      });
    });
  } catch (error) {
    console.error("Error compressing or uploading image:", error);
    throw error;
  }
};
// module.exports.uploadFileToS3 = async (file, fileName) => {
//   try {
//     // Get the file extension and determine the content type
//     const fileExtension = path.extname(fileName).slice(1);
//     const contentType = `image/${fileExtension}`;
//     // Compress the image using sharp
//     const compressedImage = await sharp(file)
//       .resize({ width: 800 }) // Resize image to a max width of 800px (you can adjust this)
//       .jpeg({ quality: 80 }) // Compress to JPEG format with 80% quality
//       .toBuffer(); // Convert the processed image to a buffer
//     // Prepare the S3 upload parameters
//     const params = {
//       Bucket: process.env.S3_BUCKET_NAME,
//       Key: fileName,
//       Body: compressedImage,
//       ContentType: contentType,
//     };

//     // Upload the compressed image to S3
//     return new Promise((resolve, reject) => {
//       s3.upload(params, function (err, data) {
//         if (err) {
//           console.log(err);
//           reject(err);
//         }
//         console.log(data);
//         resolve(data.Location);
//       });
//     });
//   } catch (error) {
//     console.error("Error compressing or uploading image:", error);
//     throw error;
//   }
// };

module.exports.getSignedUrl = async (fileName) => {
  const params = {
    Bucket: process.env.S3_BUCKET_NAME,
    Key: fileName,
    Expires: Number(process.env.SIGNED_URL_EXPIRY_TIME),
  };
  const url = await s3.getSignedUrl("getObject", params);

  return url;
};

module.exports.imageUpload = async (base64, userId) => {
  const s3 = new S3({
    region: process.env.AWS_REGION,
    accessKeyId: process.env.ACCESS_KEY_ID,
    secretAccessKey: process.env.SECRET_ACCESS_KEY,
  });

  const base64Data = new Buffer.from(
    base64.replace(/^data:image\/\w+;base64,/, ""),
    "base64"
  );

  const type = base64.split(";")[0].split("/")[1];

  const params = {
    Bucket: process.env.S3_BUCKET_NAME,
    Key: `${userId}.${type}`, // type is not required
    Body: base64Data,
    // ACL: "public-read",
    ContentEncoding: "base64", // required
    ContentType: `image/${type}`, // required. Notice the back ticks
  };
  try {
    return new Promise((resolve, reject) => {
      s3.upload(params, function (err, data) {
        if (err) {
          console.log(err);
          reject(err);
        }
        resolve(data.Location);
      });
    });
  } catch (error) {
    throw new Error(error);
  }
};
