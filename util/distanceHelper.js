const calculateDistance = async (lat1, lon1, lat2, lon2) => {
  const R = 6371;
  const toRad = (deg) => deg * (Math.PI / 180);
  console.log("lat1", lat1);
  console.log("lon1", lon1);
  console.log("lat2", lat2);
  console.log("lon2", lon2);
  const dLat = toRad(lat2 - lat1);
  const dLon = toRad(lon2 - lon1);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRad(lat1)) *
      Math.cos(toRad(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c * 1000;
  console.log("distance in helper", distance);
  return distance;
};

module.exports = {
  calculateDistance,
};
