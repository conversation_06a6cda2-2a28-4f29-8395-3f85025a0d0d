const Stripe = require("stripe");

exports.stripe = new Stripe(process.env.STRIPE_SECRET, {
  apiVersion: "2024-06-20",
  typescript: true,
});

module.exports.createCheckoutLink = (price, metadata = {}, successUrl) => {
  return new Promise(async (resolve, reject) => {
    try {
      console.log(price, metadata);
      const session = await this.stripe.checkout.sessions.create({
        payment_method_types: ["card"],
        line_items: [
          {
            price_data: {
              currency: "eur",
              product_data: {
                name: metadata.planName,
                description:
                  "Your membership will begin once approved by our admin team",
              },
              unit_amount: Math.floor(price * 100),
              recurring: {
                interval: "month",
              },
            },
            quantity: 1,
          },
        ],
        mode: "subscription",
        success_url: successUrl,
        cancel_url: process.env.BC_URI + `?type=FAIL`,
        currency: "eur",
        customer_email: metadata.email,
        metadata,
        subscription_data: {
          trial_period_days: 30, // Temporarily put them on trial (30 days)
          metadata: {
            subscriptionId: subscription._id,
          },
        },
      });

      resolve(session.url);
    } catch (error) {
      console.log(error);
      reject(error.message);
    }
  });
};
