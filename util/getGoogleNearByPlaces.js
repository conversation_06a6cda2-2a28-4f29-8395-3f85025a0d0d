const { default: axios } = require("axios");
const { S3 } = require("aws-sdk");
const crypto = require("crypto");
const { calculateDistance } = require("./distanceHelper");

function generateBitKey() {
  // Create a unique input string combining timestamp and random bytes
  const timestamp = Date.now().toString();
  const randomBytes = crypto.randomBytes(16).toString("hex");
  const inputString = timestamp + randomBytes;

  // Hash the input string using SHA-256
  const hash = crypto.createHash("sha256").update(inputString).digest("hex");

  // Truncate the hash to 16 bits and convert to hexadecimal
  const sixteenBitHash = hash.slice(0, 16);

  return sixteenBitHash;
}
// Consolidate place types to minimize API calls
const types = [
  "aquarium",
  "gym",
  "bowling_alley",
  "amusement_park",
  "shopping_mall",
  "movie_theater",
  "museum",
  "art_gallery",
  "tourist_attraction",
  "park",
];

const blacklist = [
  "car_dealer",
  "car_rental",
  "car_repair",
  "car_wash",
  "electric_vehicle_charging_station",
  "gas_station",
  "parking",
  "rest_stop",
  "farm",
  "performing_arts_theater",
  "library",
  "preschool",
  "primary_school",
  "school",
  "secondary_school",
  "university",
  "amusement_center",
  "banquet_hall",
  "casino",
  "community_center",
  "convention_center",
  "cultural_center",
  "dog_park",
  "event_venue",
  "hiking_area",
  "historical_landmark",
  "marina",
  "movie_rental",
  "national_park",
  "night_club",
  "visitor_center",
  "wedding_venue",
  "zoo",
  "accounting",
  "atm",
  "bank",
  "american_restaurant",
  "bakery",
  "bar",
  "barbecue_restaurant",
  "brazilian_restaurant",
  "breakfast_restaurant",
  "brunch_restaurant",
  "cafe",
  "chinese_restaurant",
  "coffee_shop",
  "fast_food_restaurant",
  "french_restaurant",
  "greek_restaurant",
  "hamburger_restaurant",
  "ice_cream_shop",
  "indian_restaurant",
  "indonesian_restaurant",
  "italian_restaurant",
  "japanese_restaurant",
  "korean_restaurant",
  "lebanese_restaurant",
  "meal_delivery",
  "meal_takeaway",
  "mediterranean_restaurant",
  "mexican_restaurant",
  "middle_eastern_restaurant",
  "pizza_restaurant",
  "ramen_restaurant",
  "restaurant",
  "sandwich_shop",
  "seafood_restaurant",
  "spanish_restaurant",
  "steak_house",
  "sushi_restaurant",
  "thai_restaurant",
  "turkish_restaurant",
  "vegan_restaurant",
  "vegetarian_restaurant",
  "vietnamese_restaurant",
  "administrative_area_level_1",
  "administrative_area_level_2",
  "country",
  "locality",
  "postal_code",
  "school_district",
  "city_hall",
  "courthouse",
  "embassy",
  "fire_station",
  "local_government_office",
  "police",
  "post_office",
  "dental_clinic",
  "dentist",
  "doctor",
  "drugstore",
  "hospital",
  "medical_lab",
  "pharmacy",
  "physiotherapist",
  "spa",
  "bed_and_breakfast",
  "campground",
  "camping_cabin",
  "cottage",
  "extended_stay_hotel",
  "farmstay",
  "guest_house",
  "hostel",
  "hotel",
  "lodging",
  "motel",
  "private_guest_room",
  "resort_hotel",
  "rv_park",
  "church",
  "hindu_temple",
  "mosque",
  "synagogue",
  "barber_shop",
  "beauty_salon",
  "cemetery",
  "child_care_agency",
  "consultant",
  "courier_service",
  "electrician",
  "florist",
  "funeral_home",
  "hair_care",
  "hair_salon",
  "insurance_agency",
  "laundry",
  "lawyer",
  "locksmith",
  "moving_company",
  "painter",
  "plumber",
  "real_estate_agency",
  "roofing_contractor",
  "storage",
  "tailor",
  "telecommunications_service_provider",
  "travel_agency",
  "veterinary_care",
  "auto_parts_store",
  "bicycle_store",
  "book_store",
  "cell_phone_store",
  "clothing_store",
  "convenience_store",
  "department_store",
  "discount_store",
  "electronics_store",
  "furniture_store",
  "gift_shop",
  "grocery_store",
  "hardware_store",
  "home_goods_store",
  "home_improvement_store",
  "jewelry_store",
  "liquor_store",
  "market",
  "pet_store",
  "shoe_store",
  "sporting_goods_store",
  "store",
  "supermarket",
  "wholesaler",
  "athletic_field",
  "fitness_center",
  "golf_course",
  "playground",
  "ski_resort",
  "sports_club",
  "sports_complex",
  "stadium",
  "swimming_pool",
  "airport",
  "bus_station",
  "bus_stop",
  "ferry_terminal",
  "heliport",
  "light_rail_station",
  "park_and_ride",
];

// Helper function to check if coordinates are valid
const isValidCoordinates = (coordinates) => {
  const [lat, lng] = coordinates;
  return (
    Array.isArray(coordinates) &&
    coordinates.length === 2 &&
    !isNaN(lat) &&
    !isNaN(lng) &&
    lat >= -90 &&
    lat <= 90 &&
    lng >= -180 &&
    lng <= 180
  );
};

const hasMatch = (types, placeTypes) => {
  const flatTypes = types;
  return placeTypes.find((type) => flatTypes.includes(type)) || null;
};
// const AWS = require('aws-sdk');
const s3 = new S3({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.ACCESS_KEY_ID,
  secretAccessKey: process.env.SECRET_ACCESS_KEY,
});
// const s3 = new AWS.S3();

// Helper to check if an image exists in S3
// const checkS3ImageExists = async (bucketName, key) => {
//   try {
//     await s3.headObject({ Bucket: bucketName, Key: key }).promise();
//     return true;
//   } catch (error) {
//     if (error.code === "NotFound") return false;
//     throw error;
//   }
// };
// Function to get all matching objects for a given prefix
const getS3ImagesByPrefix = async (bucketName, prefix, regionName) => {
  try {
    const params = {
      Bucket: bucketName,
      Prefix: prefix, // Match all objects starting with this prefix
    };

    const data = await s3.listObjectsV2(params).promise();
    if (data.Contents.length === 0) {
      console.log(`No objects found for prefix: ${prefix}`);
      return [];
    }

    // Construct URLs for all matched objects
    const urls = data.Contents.map(
      (item) =>
        `https://${bucketName}.s3.${regionName}.amazonaws.com/${item.Key}`
    );

    return urls;
  } catch (error) {
    console.error(`Error fetching objects from S3: ${error.message}`);
    throw error;
  }
};
// Helper to upload an image to S3
const uploadImageToS3 = async (bucketName, key, imageBuffer) => {
  try {
    await s3
      .putObject({
        Bucket: bucketName,
        Key: key,
        Body: imageBuffer,
        ContentType: "image/jpeg", // Adjust content type as needed
      })
      .promise();
  } catch (error) {
    console.error("Error uploading to S3:", error);
    throw error;
  }
};

// Modified generateGoogleApiUrls function
const generateGoogleApiUrls = async (photos, placeId) => {
  const bucketName = process.env.S3_BUCKET_NAME_GOOGLE; // Ensure you set this environment variable
  const regionName = process.env.AWS_REGION; // Ensure you set this environment variable
  const baseUrl = `https://places.googleapis.com/v1`;
  const maxWidth = 800;
  const urls = [];
  const prefix = placeId;
  // const prefix = "places/" + placeId + "photos/";
  const exists = await getS3ImagesByPrefix(bucketName, prefix, regionName);
  if (!!exists.length) {
    return exists;
    //urls.push(`https://${bucketName}.s3.${regionName}.amazonaws.com/${key}`);
  } else {
    for (const photo of photos.slice(0, 2)) {
      const photoName = photo.name.split("photos/")[0];
      const uniqueKey = generateBitKey();
      const key = `${placeId + "_" + uniqueKey}.jpg`; // Unique S3 key based on photo name
      // const key = `${photoName + "photos/" + uniqueKey}.jpg`; // Unique S3 key based on photo name

      // Fetch image from Google API
      const googleImageUrl = `${baseUrl}/${photo.name}/media?maxWidthPx=${maxWidth}&key=${process.env.MAP_KEY}`;
      const response = await axios.get(googleImageUrl, {
        responseType: "arraybuffer", // Fetch image as binary data
      });

      // Upload to S3
      await uploadImageToS3(bucketName, key, response.data);

      // Add S3 URL to list
      urls.push(`https://${bucketName}.s3.${regionName}.amazonaws.com/${key}`);
      // Check if image already exists in S3
      // const exists = await checkS3ImageExists(bucketName, key);
    }
    return urls;
  }

  // console.log("urls", urls);
};

// const generateGoogleApiUrls = (photos) => {
//   console.log("photos", photos);
//   if (!photos || !Array.isArray(photos)) return [];

//   const baseUrl = `https://places.googleapis.com/v1`;
//   const maxWidth = 800;
//   const maxHeight = 800;
//   return photos
//     .slice(0, 5)
//     .map((photo) => {
//       // if (!photo.name) return null;
//       return `${baseUrl}/${photo.name}/media?maxWidthPx=${maxWidth}&key=${process.env.MAP_KEY}`;
//     })
//     .filter((url) => url !== null);
// };

const convertArray = async (inputArray) => {
  if (!Array.isArray(inputArray)) return [];

  // Process items asynchronously
  const result = await Promise.all(
    inputArray.map(async (item) => {
      const photos = item?.photos
        ? await generateGoogleApiUrls(item?.photos, item.id) // Wait for photos to be processed
        : [];
      return {
        _id: item?.id,
        photos: photos,
        rating: item?.rating || 0,
        icon: item?.iconMaskBaseUri + ".png" || "",
        scope: "GOOGLE",
        open_now: item?.regularOpeningHours?.openNow || false,
        name: item?.displayName.text || "N/A",
        category: hasMatch(types, item?.types || []),
        googleCategory: item?.types || [],
        location: {
          type: "Point",
          coordinates: [
            item?.location?.latitude || 0,
            item?.location?.longitude || 0,
          ],
        },
        viewport: item?.viewport,
        address: item?.formattedAddress || "N/A",
        city: item?.postalAddress?.locality || "N/A",
        state: item?.postalAddress?.administrativeArea || "N/A",
        country: item?.postalAddress?.country || "N/A",
        businessSchedule: item?.regularOpeningHours?.periods || [],
        zipCode: item?.postalAddress?.postalCode || "N/A",
        periods: item?.regularOpeningHours?.periods || [],
        nextOpenTime: item?.regularOpeningHours?.nextOpenTime || "N/A",
        weekdayDescriptions:
          item?.regularOpeningHours?.weekdayDescriptions || [],
      };
    })
  );
  console.log("result in convertArray", result);
  return result;
};

const fetchPlaceData = async (types, location, radius) => {
  try {
    console.log("types", types);
    console.log("location", location);
    console.log("radius", radius);
    const requestData = {
      includedTypes: types,
      // excludedTypes: blacklist,
      maxResultCount: 3,
      locationRestriction: {
        circle: {
          center: {
            latitude: location[0],
            longitude: location[1],
          },
          radius: radius,
        },
      },
      rankPreference: "DISTANCE",
    };
    const response = await axios.post(
      "https://places.googleapis.com/v1/places:searchNearby",
      requestData,
      {
        headers: {
          "Content-Type": "application/json",
          "X-Goog-Api-Key": process.env.MAP_KEY,
          "X-Goog-FieldMask": "*", // Example FieldMask
        },
      }
    );
    console.log("response.data.places", response.data.places);
    const results = await convertArray(response.data.places.slice(0, 3));
    console.log("results in fetchPlaceData", results);
    return results;
  } catch (error) {
    console.log("error", error);
    return [];
  }
};

module.exports.getGoogleNearByPlaces = async (
  location,
  radius,
  category,
  is_open,
  price
) => {
  // const photos =
  //   "places/ChIJY_4UapwKnkcRDhKDukJU3yo/photos/AdDdOWqwK8tx593KxNNqErdDrevAC_aRt55Guz5k9HsYZovG9TBhplsPZi9GiBtvRRdBdG5cbFwGJVaCtAlx9KA3k4ETuRtk1aoNYXX_JqOHydAyYD-BNR5ua-E2BlwYBedivy9fc90A3lsWU7a5In5h4qRF311IgrHT29qS";
  // const photoName = photos.split("photos/")[0];
  // const uniqueKey = generateBitKey();
  // // const key = `${photoName + uniqueKey}.jpg`;
  // const key = `${photoName + "photos/" + uniqueKey}.jpg`;
  // console.log("key", key);
  // const prefix = photoName + "photos/";
  // console.log("prefix", prefix);
  // return [key];
  try {
    if (!isValidCoordinates(location)) {
      throw new Error("Invalid coordinates provided");
    }

    // radius = Math.min(Math.max(radius || 5000, 1000), 50000);

    // Determine which types to search for
    const placetypes = !!category.length ? category : types;
    const skipTypes = [
      "Restaurant",
      "restaurant",
      "Cafe",
      "cafe",
      "bar",
      "Bar",
    ];

    const filteredTypes = placetypes.filter(
      (type) => !skipTypes.includes(type)
    );
    // Make a single API call with combined types
    let results = await fetchPlaceData(filteredTypes, location, radius);
    console.log("results", results);
    // Apply filters to the combined results
    if (is_open) {
      results = results.filter((ele) => ele.open_now === true);
    }

    // Sort by rating
    // results.sort((a, b) => (b.rating || 0) - (a.rating || 0));

    // calculate distance
    if (results?.length > 0)
      results.forEach(async (result) => {
        const distance = await calculateDistance(
          location[0],
          location[1],
          result?.location?.coordinates[0],
          result?.location?.coordinates[1]
        );
        console.log("distance in getGoogleNearByPlaces", distance);
        result.distance = distance;
      });

    return results;
  } catch (error) {
    console.error("Error in getGoogleNearByPlaces:", error.message);
    return [];
  }
};
