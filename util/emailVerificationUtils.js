const { userApp } = require("../util/firebaseConfig"); 
const User = require("../models/User/User"); // Adjust path as needed

const checkAndUpdateEmailVerification = async (email) => {
    try {
    
      const firebaseUser = await userApp.auth().getUserByEmail(email);
   // Check if the email is verified
      const isEmailVerified = firebaseUser.emailVerified;
  
     
      await User.updateOne(
        { email },
        { $set: { isEmailVerified } }
      );
  
      return firebaseUser;
    } catch (error) {
      console.log("Error verifying email in Firebase:", error.message);
  
     
      await User.updateOne(
        { email },
        { $set: { isEmailVerified: false } }
      );
  
     
      return null;
    }
  };
  
  module.exports = { checkAndUpdateEmailVerification };
  
