const admin = require('firebase-admin');
const dotenv = require('dotenv');

dotenv.config();

// Initialize Firebase Admin SDK
// You need to create a service account in Firebase console and download the JSON file
// Then add the path to your .env file as FIREBASE_SERVICE_ACCOUNT_PATH
let serviceAccount;
try {
  serviceAccount = require(process.env.FIREBASE_SERVICE_ACCOUNT_PATH);
} catch (error) {
  console.error('Error loading Firebase service account file:', error);
  // Fallback to environment variables if file not found
  serviceAccount = {
    type: process.env.FIREBASE_TYPE,
    project_id: process.env.FIREBASE_PROJECT_ID,
    private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
    private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    client_email: process.env.FIREBASE_CLIENT_EMAIL,
    client_id: process.env.FIREBASE_CLIENT_ID,
    auth_uri: process.env.FIREBASE_AUTH_URI,
    token_uri: process.env.FIREBASE_TOKEN_URI,
    auth_provider_x509_cert_url: process.env.FIREBASE_AUTH_PROVIDER_CERT_URL,
    client_x509_cert_url: process.env.FIREBASE_CLIENT_CERT_URL
  };
}

// Initialize the app
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: process.env.FIREBASE_DATABASE_URL,
    storageBucket: process.env.FIREBASE_STORAGE_BUCKET
  });
}

// Export the admin SDK and Firestore
const db = admin.firestore();
const messaging = admin.messaging();
const auth = admin.auth();
const storage = admin.storage();

module.exports = {
  admin,
  db,
  messaging,
  auth,
  storage
};
