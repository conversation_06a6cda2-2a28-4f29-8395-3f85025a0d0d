// Helper function to parse time strings like '15m', '1h', '30d', etc.
const parseTimeString = (envVar, defaultSeconds) => {
  const envValue = process.env[envVar];
  if (!envValue) return defaultSeconds;

  // If it's a number string, parse it directly
  if (!isNaN(Number(envValue))) return Number(envValue);

  // Handle time unit strings like '30d', '24h', etc.
  const match = envValue.match(/^(\d+)([dhms])$/);
  if (match) {
    const value = Number(match[1]);
    const unit = match[2];

    switch (unit) {
      case "d":
        return value * 24 * 60 * 60; // days to seconds
      case "h":
        return value * 60 * 60; // hours to seconds
      case "m":
        return value * 60; // minutes to seconds
      case "s":
        return value; // already in seconds
      default:
        return defaultSeconds; // fallback to default
    }
  }

  // Fallback to default if format is unrecognized
  console.warn(`Unrecognized ${envVar} format: ${envValue}, using default`);
  return defaultSeconds;
};

// Access token expiration time (15 minutes in seconds)
module.exports.ACCESS_TOKEN_AGE = parseTimeString("JWT_TOKEN_AGE", 60 * 15);

// Refresh token expiration time (30 days in seconds)
module.exports.REFRESH_TOKEN_AGE = parseTimeString(
  "REFRESH_TOKEN_AGE",
  60 * 60 * 24 * 30
);

// Legacy token expiration time (3 days in seconds) - kept for backward compatibility
module.exports.USER_TOKEN_AGE = parseTimeString(
  "USER_TOKEN_AGE",
  60 * 60 * 24 * 3
);

// Token secrets
module.exports.TOKEN_SECRET = process.env.JWT_SECRET || "TOKENSECRET";
module.exports.REFRESH_TOKEN_SECRET =
  process.env.REFRESH_TOKEN_SECRET || "REFRESHTOKENSECRET";
