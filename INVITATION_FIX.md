# Invitation System Fix

## Issue
When a user has already received an invitation from user <PERSON> and is sending an invitation to the same user A for the same time, it was triggering case 2A instead of case 2B.

## Root Cause
The code was checking Case 2A (general pending invitations) first, and if it found a match, it returned immediately without checking Case 2B (specific invitations from the invitee to the current user). This meant that when user <PERSON> tried to send an invitation to user <PERSON> who had already sent an invitation to user <PERSON>, it would show the generic Case 2A message instead of the more specific Case 2B message.

## Solution
1. Reordered the checks to check Case 2B first for single-user invitations
2. Modified the Case 2A check to exclude invitations from the current invitee when it's a single-user invitation
3. Added tests to verify the fix works correctly

## Changes Made
1. In `invitationController.js`:
   - Moved the Case 2B check before the Case 2A check for single-user invitations
   - Added a filter to exclude invitations from the current invitee in the Case 2A check
   - Fixed the code structure to maintain proper variable scoping

2. Added tests:
   - Created a test file `invitationController.test.js` to verify the fix
   - Added test cases for both Case 2A and Case 2B scenarios
   - Updated package.json to include test dependencies

## How to Test
Run the tests with:
```
npm test
```

The tests verify that:
1. When user <PERSON> sends an invitation to user <PERSON> who already sent an invitation to user <PERSON>, it triggers Case 2B
2. When user <PERSON> sends an invitation to user <PERSON> while having a pending invitation from user D, it triggers Case 2A

## Expected Behavior
- When a user tries to send an invitation to someone who has already sent them an invitation for the same time, they should see the Case 2B message with options to accept the invitation or decline it.
- When a user tries to send an invitation while having other pending invitations for the same time (not from the current invitee), they should see the Case 2A message with options to send anyway or cancel.