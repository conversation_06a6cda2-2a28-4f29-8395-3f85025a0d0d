const jwt = require("jsonwebtoken");
const crypto = require("crypto");
const {
  ACCESS_TOKEN_AGE,
  REFRESH_TOKEN_AGE,
  USER_TOKEN_AGE,
  TOKEN_SECRET,
  REFRESH_TOKEN_SECRET,
} = require("../config/config");
const RefreshToken = require("../models/User/RefreshToken");

/**
 * Legacy token creation function - kept for backward compatibility
 */
module.exports.createToken = async (user) => {
  let expiresIn = USER_TOKEN_AGE;
  const token = await jwt.sign({ ...user._doc }, TOKEN_SECRET, {
    expiresIn,
  });
  return token;
};

/**
 * Creates both access and refresh tokens
 * @param {Object} user - User object
 * @param {String} userModel - Model name ('user', 'admin', or 'partner')
 * @param {Object} metadata - Additional metadata (userAgent, ipAddress)
 * @returns {Object} Object containing access and refresh tokens
 */
module.exports.createTokenPair = async (
  user,
  userModel = "user",
  metadata = {}
) => {
  // Create access token (short-lived)
  const accessToken = jwt.sign(
    {
      ...user._doc,
      tokenType: "access",
    },
    TOKEN_SECRET,
    { expiresIn: ACCESS_TOKEN_AGE }
  );

  // Create refresh token (long-lived)
  const refreshTokenString = crypto.randomBytes(40).toString("hex");

  // Calculate expiry date - using milliseconds for better reliability
  const expiresAt = new Date();
  try {
    // Make sure REFRESH_TOKEN_AGE is a number
    const refreshTokenAgeInSeconds = Number(REFRESH_TOKEN_AGE);
    if (isNaN(refreshTokenAgeInSeconds)) {
      console.error(
        "REFRESH_TOKEN_AGE is not a valid number:",
        REFRESH_TOKEN_AGE
      );
      throw new Error("Invalid REFRESH_TOKEN_AGE configuration");
    }

    // Add seconds to current time
    expiresAt.setTime(expiresAt.getTime() + refreshTokenAgeInSeconds * 1000);

    // Validate the resulting date
    if (isNaN(expiresAt.getTime())) {
      console.error("Generated an invalid date:", expiresAt);
      throw new Error("Invalid expiration date generated");
    }

    // console.log("Successfully created expiry date:", expiresAt);
  } catch (error) {
    console.error("Error setting expiry date:", error);
    // Fallback to a safe default (30 days)
    expiresAt.setTime(expiresAt.getTime() + 60 * 60 * 24 * 30 * 1000);
    // console.log("Using fallback expiry date:", expiresAt);
  }

  // Log for debugging
  // console.log("Creating refresh token with expiry:", expiresAt);
  // console.log("REFRESH_TOKEN_AGE in seconds:", REFRESH_TOKEN_AGE);

  try {
    // Save refresh token to database
    const refreshToken = await RefreshToken.create({
      userId: user._id,
      userModel,
      token: refreshTokenString,
      expiresAt,
      userAgent: metadata?.userAgent,
      ipAddress: metadata?.ipAddress,
    });

    return {
      accessToken,
      refreshToken: refreshTokenString,
      refreshTokenId: refreshToken._id,
      expiresAt: expiresAt,
    };
  } catch (error) {
    console.error("Error creating refresh token:", error);
    // Fallback to just returning the access token if refresh token creation fails
    return {
      accessToken,
      error: error.message,
    };
  }

  // This code is unreachable due to the try/catch block above
};

/**
 * Verifies a refresh token and issues a new access token
 * @param {String} refreshToken - The refresh token string
 * @returns {Object} New tokens or null if invalid
 */
module.exports.refreshAccessToken = async (refreshToken) => {
  try {
    // Find the refresh token in the database
    const tokenDoc = await RefreshToken.findOne({
      token: refreshToken,
      isRevoked: false,
      expiresAt: { $gt: new Date() },
    });

    if (!tokenDoc) {
      return null;
    }

    // Determine which model to use based on userModel field
    let UserModel;
    switch (tokenDoc.userModel) {
      case "admin":
        UserModel = require("../models/Admin/Admin");
        break;
      case "partner":
        UserModel = require("../models/Partner/Partner");
        break;
      default:
        UserModel = require("../models/User/User");
    }

    // Find the user
    const user = await UserModel.findById(tokenDoc.userId);
    if (!user) {
      return null;
    }

    // Create a new access token
    const accessToken = jwt.sign(
      {
        ...user._doc,
        tokenType: "access",
      },
      TOKEN_SECRET,
      { expiresIn: ACCESS_TOKEN_AGE }
    );

    return {
      accessToken,
      refreshToken: tokenDoc.token,
      user,
    };
  } catch (error) {
    console.error("Error refreshing token:", error);
    return null;
  }
};

/**
 * Revokes a refresh token
 * @param {String} refreshToken - The refresh token to revoke
 * @returns {Boolean} Success status
 */
module.exports.revokeRefreshToken = async (refreshToken) => {
  try {
    const result = await RefreshToken.updateOne(
      { token: refreshToken },
      { isRevoked: true }
    );
    return result.modifiedCount > 0;
  } catch (error) {
    console.error("Error revoking token:", error);
    return false;
  }
};
