const { getUser } = require("../util/getUser");

module.exports.optionalAuth = async (req, res, next) => {
  let token = req.headers.authorization;

  if (token) {
    token = token.replace("Bearer ", "");
    await getUser(token, (user) => {
      if (user) {
        req.user = user;
      } else {
        req.user = null;
      }
      next();
    });
  } else {
    req.user = null; 
    next();
  }
};
