module.exports.checkGuestAccess = () => {
  return async (req, res, next) => {
    try {
      const auth = {
        username: process.env.BASIC_AUTH_USERNAME,
        password: process.env.BASIC_AUTH_PASSWORD,
      };

      // Skip auth check if credentials are not configured (for development)
      if (!auth.username || !auth.password) {
        console.warn(
          "Basic auth credentials not configured, skipping auth check"
        );
        return next();
      }

      // Check for authorization in headers or query parameters (for mobile apps)
      const authHeader = req.headers.authorization || "";
      const queryAuth = req.query.authorization;

      // If no auth header is present and no query auth
      if (!authHeader && !queryAuth) {
        // For mobile apps, we'll be more lenient in development
        if (
          process.env.NODE_ENV !== "production" &&
          req.headers["user-agent"] &&
          (req.headers["user-agent"].includes("Flutter") ||
            req.headers["user-agent"].includes("okhttp") ||
            req.headers["user-agent"].includes("Dart"))
        ) {
          console.warn(
            "Mobile app detected without auth, allowing access in non-production"
          );
          return next();
        }

        return res.status(401).json({
          error: "Authentication required",
          status: false,
          message: "Please provide valid authentication credentials",
        });
      }

      // Get the base64 credentials from either header or query parameter
      let b64auth = "";

      if (authHeader) {
        // Parse the authorization header
        b64auth = authHeader.split(" ")[1] || "";
      } else if (queryAuth) {
        // Use the query parameter directly
        b64auth = queryAuth;
      }

      if (!b64auth) {
        return res.status(401).json({
          error: "Invalid authorization format",
          status: false,
          message:
            "Authorization format should be 'Basic <credentials>' or provided as a query parameter",
        });
      }

      // Decode the base64 credentials
      let decodedAuth;
      try {
        decodedAuth = Buffer.from(b64auth, "base64").toString();
      } catch (e) {
        // For Flutter apps, try without base64 decoding (they might send plain credentials)
        if (
          req.headers["user-agent"] &&
          req.headers["user-agent"].includes("Flutter")
        ) {
          console.warn(
            "Attempting to use credentials without base64 decoding for Flutter app"
          );
          decodedAuth = b64auth;
        } else {
          return res.status(401).json({
            error: "Invalid authorization encoding",
            status: false,
            message: "Could not decode authorization credentials",
          });
        }
      }

      // Split username and password
      const [username, password] = decodedAuth.split(":");

      // Validate credentials
      if (
        username &&
        password &&
        username === auth.username &&
        password === auth.password
      ) {
        return next();
      }

      // Authentication failed
      return res.status(401).json({
        error: "Invalid credentials",
        status: false,
        message: "The provided authentication credentials are invalid",
      });
    } catch (error) {
      console.error("Error in checkGuestAccess middleware:", error);
      return res.status(500).json({
        error: "Authentication error",
        status: false,
        message: "An error occurred during authentication",
      });
    }
  };
};
