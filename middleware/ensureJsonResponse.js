/**
 * Middleware to ensure all API responses have the correct Content-Type header
 * This helps prevent HTML responses from being sent to clients expecting JSON
 */
module.exports = function ensureJsonResponse(req, res, next) {
  // Set Content-Type header for all responses
  res.setHeader("Content-Type", "application/json");

  // Store the original res.send and res.json methods
  const originalSend = res.send;
  const originalJson = res.json;
  const originalEnd = res.end;

  // Override res.send to ensure JSON responses
  res.send = function (body) {
    // If body is not an object or JSON string, convert it to a JSON object
    if (typeof body === "string" && body.trim().startsWith("<")) {
      console.warn(
        "Attempted to send HTML response, converting to JSON:",
        body.length > 100 ? body.substring(0, 100) + "..." : body
      );

      return originalJson.call(this, {
        error: "Internal server error",
        status: false,
        originalContentType: "text/html",
      });
    }

    // If body is a string but not HTML, try to parse it as <PERSON>SO<PERSON>
    if (typeof body === "string" && !body.trim().startsWith("<")) {
      try {
        // Check if it's already valid JSON
        JSON.parse(body);
        // If it parsed successfully, it's already JSON, so just send it
      } catch (e) {
        // Not valid JSON, so wrap it in a JSON object
        console.warn(
          "String response not in JSON format, converting to JSON object"
        );
        return originalJson.call(this, {
          message: body,
          status: true,
        });
      }
    }

    return originalSend.apply(this, arguments);
  };

  // Override res.json to ensure proper headers
  res.json = function (body) {
    // Ensure Content-Type is set to application/json
    this.setHeader("Content-Type", "application/json");
    return originalJson.apply(this, arguments);
  };

  // Override res.end to catch empty responses
  res.end = function (chunk, encoding) {
    // If it's an OPTIONS request, make sure it has proper headers
    if (req.method === "OPTIONS") {
      // Ensure CORS headers are set
      if (!res.headersSent) {
        res.statusCode = 200;
        res.setHeader("Content-Type", "application/json");
      }
    }

    return originalEnd.apply(this, arguments);
  };

  // Continue to the next middleware
  next();
};
