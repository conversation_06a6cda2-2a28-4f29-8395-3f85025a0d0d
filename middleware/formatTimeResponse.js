/**
 * Middleware to automatically format all date/time fields in API responses to ISO 8601 UTC format
 * This ensures consistent timezone handling across all API endpoints
 */

const { formatForAPI } = require("../util/timeUtils");

/**
 * Recursively traverse an object and format all Date objects to ISO 8601 UTC strings
 * @param {*} obj - Object to traverse and format
 * @param {Set} visited - Set to track visited objects to prevent infinite recursion
 * @param {number} depth - Current recursion depth
 * @param {number} maxDepth - Maximum recursion depth allowed
 * @returns {*} - Object with formatted dates
 */
function formatDatesInObject(
  obj,
  visited = new Set(),
  depth = 0,
  maxDepth = 10
) {
  // Prevent deep recursion
  if (depth > maxDepth) {
    return obj;
  }
  if (obj === null || obj === undefined) {
    return obj;
  }

  // If it's a Date object, format it to ISO 8601 UTC
  if (obj instanceof Date) {
    return formatForAPI(obj);
  }

  // For primitive types, return as-is
  if (typeof obj !== "object") {
    return obj;
  }

  // Prevent infinite recursion by checking if we've already visited this object
  if (visited.has(obj)) {
    return obj;
  }

  // Handle Mongoose documents by converting to plain object first
  if (obj.toObject && typeof obj.toObject === "function") {
    try {
      obj = obj.toObject();
    } catch (error) {
      // If toObject fails, try toJSON
      if (obj.toJSON && typeof obj.toJSON === "function") {
        try {
          obj = obj.toJSON();
        } catch (jsonError) {
          // If both fail, return the original object
          return obj;
        }
      } else {
        return obj;
      }
    }
  }

  // Add to visited set
  visited.add(obj);

  try {
    // If it's an array, recursively format each element
    if (Array.isArray(obj)) {
      const result = obj.map((item) =>
        formatDatesInObject(item, visited, depth + 1, maxDepth)
      );
      visited.delete(obj);
      return result;
    }

    // If it's an object, recursively format its properties
    const formatted = {};
    for (const [key, value] of Object.entries(obj)) {
      // Skip Mongoose internal properties and functions
      if (
        key.startsWith("_") ||
        key.startsWith("$") ||
        typeof value === "function"
      ) {
        continue;
      }

      // Handle common date field names
      if (isDateField(key) && value instanceof Date) {
        formatted[key] = formatForAPI(value);
      } else {
        formatted[key] = formatDatesInObject(
          value,
          visited,
          depth + 1,
          maxDepth
        );
      }
    }

    visited.delete(obj);
    return formatted;
  } catch (error) {
    visited.delete(obj);
    // If formatting fails, return the original object
    return obj;
  }
}

/**
 * Check if a field name indicates it contains a date/time value
 * @param {string} fieldName - Name of the field
 * @returns {boolean} - True if field likely contains a date/time
 */
function isDateField(fieldName) {
  const dateFieldPatterns = [
    /.*at$/i, // createdAt, updatedAt, deletedAt, etc.
    /.*time$/i, // lastOnlineTime, chatOpenTime, etc.
    /^date$/i, // date field
    /.*date$/i, // releaseDate, expireDate, etc.
    /timestamp/i, // timestamp fields
    /^time$/i, // time field
    /joinedAt/i, // joinedAt
    /acceptedAt/i, // acceptedAt
    /sentAt/i, // sentAt
    /expiredAt/i, // expiredAt
    /scheduledTime/i, // scheduledTime
    /chatOpenTime/i, // chatOpenTime
    /chatCloseTime/i, // chatCloseTime
  ];

  return dateFieldPatterns.some((pattern) => pattern.test(fieldName));
}

/**
 * Safer date formatting function for API responses
 * Only formats dates at shallow levels to avoid recursion issues
 */
function formatDatesShallow(obj) {
  if (!obj || typeof obj !== "object") {
    return obj;
  }

  // Handle Mongoose documents
  if (obj.toObject && typeof obj.toObject === "function") {
    try {
      obj = obj.toObject();
    } catch (error) {
      if (obj.toJSON && typeof obj.toJSON === "function") {
        try {
          obj = obj.toJSON();
        } catch (jsonError) {
          return obj;
        }
      } else {
        return obj;
      }
    }
  }

  // If it's an array, format each element (but only one level deep)
  if (Array.isArray(obj)) {
    return obj.map((item) => {
      if (item instanceof Date) {
        return formatForAPI(item);
      } else if (item && typeof item === "object") {
        // For objects in arrays, only format direct date properties
        const formatted = {};
        for (const [key, value] of Object.entries(item)) {
          if (
            key.startsWith("_") ||
            key.startsWith("$") ||
            typeof value === "function"
          ) {
            continue;
          }
          if (value instanceof Date) {
            formatted[key] = formatForAPI(value);
          } else {
            formatted[key] = value;
          }
        }
        return formatted;
      }
      return item;
    });
  }

  // For objects, format date properties
  const formatted = {};
  for (const [key, value] of Object.entries(obj)) {
    if (
      key.startsWith("_") ||
      key.startsWith("$") ||
      typeof value === "function"
    ) {
      continue;
    }

    if (value instanceof Date) {
      formatted[key] = formatForAPI(value);
    } else if (Array.isArray(value)) {
      // Handle arrays of objects with dates
      formatted[key] = value.map((item) => {
        if (item instanceof Date) {
          return formatForAPI(item);
        } else if (item && typeof item === "object") {
          const itemFormatted = {};
          for (const [itemKey, itemValue] of Object.entries(item)) {
            if (
              itemKey.startsWith("_") ||
              itemKey.startsWith("$") ||
              typeof itemValue === "function"
            ) {
              continue;
            }
            if (itemValue instanceof Date) {
              itemFormatted[itemKey] = formatForAPI(itemValue);
            } else {
              itemFormatted[itemKey] = itemValue;
            }
          }
          return itemFormatted;
        }
        return item;
      });
    } else if (
      value &&
      typeof value === "object" &&
      (!value.constructor || value.constructor === Object)
    ) {
      // Handle nested objects (one level only)
      const nestedFormatted = {};
      for (const [nestedKey, nestedValue] of Object.entries(value)) {
        if (
          nestedKey.startsWith("_") ||
          nestedKey.startsWith("$") ||
          typeof nestedValue === "function"
        ) {
          continue;
        }
        if (nestedValue instanceof Date) {
          nestedFormatted[nestedKey] = formatForAPI(nestedValue);
        } else {
          nestedFormatted[nestedKey] = nestedValue;
        }
      }
      formatted[key] = nestedFormatted;
    } else {
      formatted[key] = value;
    }
  }

  return formatted;
}

/**
 * Middleware function to format time fields in API responses
 */
function formatTimeResponse(req, res, next) {
  // Store the original res.json method
  const originalJson = res.json;

  // Override res.json to format dates before sending
  res.json = function (body) {
    try {
      // Only format if body is an object (not string, number, etc.)
      if (body && typeof body === "object") {
        // Use the safer shallow formatting approach
        const formattedBody = formatDatesShallow(body);
        return originalJson.call(this, formattedBody);
      } else {
        return originalJson.call(this, body);
      }
    } catch (error) {
      console.error("Error formatting dates in API response:", error);
      // If formatting fails, send original response
      return originalJson.call(this, body);
    }
  };

  next();
}

/**
 * Utility function to manually format a response object
 * Use this when you need to format dates before sending a response
 * @param {*} data - Data to format
 * @returns {*} - Formatted data
 */
function formatResponseData(data) {
  return formatDatesInObject(data);
}

module.exports = {
  formatTimeResponse,
  formatResponseData,
  formatDatesInObject,
  formatDatesShallow,
  isDateField,
};
