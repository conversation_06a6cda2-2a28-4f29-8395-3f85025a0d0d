/**
 * Middleware to debug CORS issues
 * This middleware logs information about incoming requests that might be useful for debugging CORS problems
 */
module.exports = function corsDebugger(req, res, next) {
  // Only log if DEBUG_CORS environment variable is set
  if (process.env.DEBUG_CORS === 'true') {
    console.log('\n--- CORS Debug Info ---');
    console.log('Request URL:', req.originalUrl);
    console.log('Request Method:', req.method);
    console.log('Request Headers:');
    
    // Log important headers
    const headersToLog = [
      'origin', 
      'host', 
      'referer', 
      'user-agent', 
      'content-type', 
      'accept', 
      'authorization'
    ];
    
    headersToLog.forEach(header => {
      if (req.headers[header]) {
        // For authorization, don't log the actual token
        if (header === 'authorization') {
          console.log(`  ${header}: [REDACTED]`);
        } else {
          console.log(`  ${header}: ${req.headers[header]}`);
        }
      }
    });
    
    // Store original end method
    const originalEnd = res.end;
    
    // Override end method to log response info
    res.end = function(...args) {
      console.log('Response Status:', res.statusCode);
      console.log('Response Headers:');
      
      // Log CORS-related headers
      const corsHeaders = [
        'access-control-allow-origin',
        'access-control-allow-methods',
        'access-control-allow-headers',
        'access-control-allow-credentials',
        'access-control-max-age',
        'access-control-expose-headers'
      ];
      
      corsHeaders.forEach(header => {
        if (res.getHeader(header)) {
          console.log(`  ${header}: ${res.getHeader(header)}`);
        }
      });
      
      console.log('--- End CORS Debug ---\n');
      
      // Call original end method
      return originalEnd.apply(this, args);
    };
  }
  
  next();
};
