const {
  getPartnerService,
  createPartnerService,
} = require("../../services/partner.services");

exports.getPartner = async (req, res) => {
  try {
    const result = await getPartnerService();
    res.status(200).json({
      status: "success",
      data: result,
    });
  } catch (error) {
    res.status(400).json({
      status: "failed",
      message: "user fetch error",
      error: error.message,
    });
  }
};
exports.createPartner = async (req, res) => {
  try {
    const result = await createPartnerService(req.body);
    res.status(200).json({
      status: "success",
      data: [],
    });
  } catch (error) {
    console.log("er", error);
    res.status(400).json({
      status: "failed",
      message: "user create error",
      error: error.message,
    });
  }
};
