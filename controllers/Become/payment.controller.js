// const { stripe, createCheckoutLink } = require("../utils/stripe");

const { ObjectId } = require("mongodb");
const Stripe = require("stripe");
const Subscription = require("../../models/Admin/subscription");
const { calculateExpiryDate } = require("../../util/calculateExpiryDate");
const PartnerSubscription = require("../../models/Partner/partnerSubscription");

exports.stripe = new Stripe(process.env.STRIPE_SECRET, {
  apiVersion: "2024-06-20",
  typescript: true,
});

exports.createStripePayment = async (req, res) => {
  try {
    const { purchaseType, selected, discount, userId, successUrl, voucher } =
      req.body;
    const subscriptionId = selected._id;
    // console.log("voucher", voucher);
    // console.log("discount", discount);
    // Find the subscription package
    const findSubscription = await Subscription.findOne({
      _id: subscriptionId,
      userType: "PARTNER",
      status: true,
    });

    if (!findSubscription) {
      return res.status(400).json({ message: "Subscription not found" });
    }

    const originalPrice = findSubscription.price;
    let discountedPrice = originalPrice;
    let additionalMonths = 0;
    let stripeCoupon = null; // This will hold the Stripe coupon ID if applicable

    // Handle the discount logic
    if (discount) {
      switch (discount) {
        case "25% off":
          stripeCoupon = process.env.OFF_25_COUPON; // Replace with your actual Stripe coupon ID
          additionalMonths = findSubscription.timePeriod;
          break;
        case "50% off":
          stripeCoupon = process.env.OFF_50_COUPON;
          additionalMonths = findSubscription.timePeriod;
          break;
        case "75% off":
          stripeCoupon = process.env.OFF_75_COUPON;
          additionalMonths = findSubscription.timePeriod;
          break;
        case "Buy 1 Get 1 free":
          additionalMonths = Number(findSubscription.timePeriod) + 1;
          // stripeCoupon = process.env.BUY_ONE_GET_ONE_FREE;
          break;
        case "1 month free":
          additionalMonths = 1;
          stripeCoupon = process.env.ONE_MONTH_FREE_COUPON;
          break;
        case "3 months free":
          additionalMonths = 3;
          stripeCoupon = process.env.THREE_MONTHS_FREE_COUPON;
          break;
        case "6 months free":
          additionalMonths = 6;
          stripeCoupon = process.env.SIX_MONTHS_FREE_COUPON;
          break;
        case "12 months free":
          additionalMonths = 12;
          stripeCoupon = process.env.TWELVE_MONTHS_FREE_COUPON;
          break;
        default:
          return res.status(400).json({ message: "Invalid voucher" });
      }
    }
    // console.log("additionalMonths", additionalMonths);
    // Calculate the new expiry date based on additional months
    const expireAt = calculateExpiryDate(Number(additionalMonths));

    // If additional months are provided, update the subscription duration
    // if (additionalMonths > 0 && discount !== "Buy 1 Get 1 free") {
    //   await PartnerSubscription.create({
    //     partnerId: userId,
    //     expireAt,
    //     timePeriod: Number(additionalMonths),
    //     usedVoucher: voucher,
    //     isActive: false,
    //     subscriptionId,
    //   });
    //   return res
    //     .status(200)
    //     .json({ message: "Subscription extended with free months." });
    // } else {
    const currentDate = new Date();
    const futureDate = new Date(
      currentDate.getTime() + 14 * 24 * 60 * 60 * 1000
    );
    // Create a Stripe checkout session for percentage-based discounts or no discount
    const sessionParams = {
      payment_method_types: ["card"],
      line_items: [
        {
          price_data: {
            currency: "eur",
            product_data: {
              name: findSubscription.planName, //`Subscription Plan: ${findSubscription.planName}`,
            },
            recurring: {
              interval: "month", // or 'year'
            },
            unit_amount: discountedPrice * 100, // Stripe expects amount in cents
          },
          quantity: 1,
        },
      ],
      mode: "subscription", // Use "subscription" mode for recurring payments
      success_url: successUrl,
      cancel_url: process.env.CANCEL_URL,
      customer_email: req.user?.email,
      metadata: {
        trial_start: futureDate,
        // userId: req.user?._id ?? userId,
        userType: req.user?.userType ?? "PARTNER",
        subscriptionId,
        usedVoucher: voucher ? voucher : null,
        month: Number(additionalMonths),
        isUpgrade: purchaseType === "UPGRADE" ? true : false,
        isActive: false,
        hasFreeMonth: discount === "Buy 1 Get 1 free" ? true : false,
      },
      subscription_data: {
        metadata: {
          trial_start: futureDate,
          // userId: req.user?._id ?? userId,
          userType: req.user?.userType ?? "PARTNER",
          subscriptionId,
          usedVoucher: voucher ? voucher : null,
          month: Number(additionalMonths),
          isUpgrade: purchaseType === "UPGRADE" ? true : false,
          isActive: false,
          purchaseType,
          hasFreeMonth: discount === "Buy 1 Get 1 free" ? true : false,
        },
      },
    };

    // Apply the Stripe coupon if applicable
    if (stripeCoupon) {
      sessionParams.discounts = [{ coupon: stripeCoupon }];
    }

    // Create the checkout session
    const session = await this.stripe.checkout.sessions.create(sessionParams);

    res.status(200).json({
      url: session?.url,
      sessionId: session?.id,
      message: "Fetched successfully",
    });
    // }
  } catch (err) {
    console.log(err);
    res.status(400).json({ error: err.message });
  }
};
