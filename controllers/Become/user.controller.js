const Newsletter = require("../../models/User/Newsletter");
const {
  getUserService,
  createUserService,
  uploadFileService,
  sendEmailService,
  sendGuestListEmail,
} = require("../../services/user.services");

exports.getUsers = async (req, res) => {
  try {
    const result = await getUserService();
    res.status(200).json({
      status: "success",
      data: result,
    });
  } catch (error) {
    res.status(400).json({
      status: "failed",
      message: "user fetch error",
      error: error.message,
    });
  }
};
exports.createUser = async (req, res) => {
  try {
    const result = await createUserService(req.body);
    res.status(200).json({
      status: "success",
      data: result,
    });
  } catch (error) {
    console.log("er", error);
    res.status(400).json({
      status: "failed",
      message: "user create error",
      error: error.message,
    });
  }
};

exports.sendEmail = async (req, res) => {
  try {
    await sendEmailService(req.body);
    res.status(200).json({
      status: "success",
      data: {},
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: "failed",
      message: "Sending Message failed",
      error: error.message,
    });
  }
};
exports.sendGuestListEmail = async (req, res) => {
  try {
    await sendGuestListEmail(req.body);
    res.status(200).json({
      status: "success",
      data: {},
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: "failed",
      message: "Sending Message failed",
      error: error.message,
    });
  }
};
exports.saveNewsletter = async (req, res) => {
  try {
    const { email } = req.body;
    const exists = await Newsletter.findOne({ email });
    if (exists) {
      res.status(409).json({
        status: "conflict",
        data: { message: "Provided Email Already Subscribed" },
      });
    } else {
      const saveNewsletter = await Newsletter.create({
        email,
      });
      res.status(200).json({
        status: "success",
        data: saveNewsletter,
      });
    }
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: "failed",
      error: error.message,
    });
  }
};

exports.uploadFile = async (req, res) => {
  try {
    // console.log("I called upload");
    const url = await uploadFileService(req.file);
    res.status(200).json({
      status: "success",
      data: {
        url,
      },
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: "failed",
      message: "File upload error",
      error: error.message,
    });
  }
};
