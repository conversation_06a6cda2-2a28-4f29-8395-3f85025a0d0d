const voucherService = require("../../services/voucher.services");

const verifyVoucher = async (req, res) => {
  try {
    const voucherCode = req.params.voucherCode;
    const result = await voucherService.checkVoucher(voucherCode);
    if (result.valid) {
      res.status(200).json({ discount: result.discount });
    } else {
      res.status(400).json({ message: result.message });
    }
  } catch (error) {
    res.status(500).json({ message: "Server error" });
  }
};

module.exports = { verifyVoucher };
