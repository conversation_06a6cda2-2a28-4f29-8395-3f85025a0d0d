const { getPackageService } = require("../../services/package.services");

exports.getPackages = async (req, res) => {
  try {
    const result = await getPackageService();
    res.status(200).json({
      status: "success",
      data: result,
    });
  } catch (error) {
    res.status(400).json({
      status: "failed",
      message: "package fetch error",
      error: error.message,
    });
  }
};
