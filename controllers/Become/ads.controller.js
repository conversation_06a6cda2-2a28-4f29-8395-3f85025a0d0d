const {
  getAdsService,
  createAdsService,
  uploadFileService,
} = require("../../services/ads.services");

exports.getAds = async (req, res) => {
  try {
    const result = await getAdsService();
    res.status(200).json({
      status: "success",
      data: result,
    });
  } catch (error) {
    res.status(400).json({
      status: "failed",
      message: "user fetch error",
      error: error.message,
    });
  }
};
exports.createAds = async (req, res) => {
  try {
    const result = await createAdsService(req.body);
    res.status(200).json({
      status: "success",
      data: [],
    });
  } catch (error) {
    console.log("er", error);
    res.status(400).json({
      status: "failed",
      message: "user create error",
      error: error.message,
    });
  }
};

exports.uploadFile = async (req, res) => {
  try {
    const url = await uploadFileService(req.file);
    res.status(200).json({
      status: "success",
      data: {
        url,
      },
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: "failed",
      message: "File upload error",
      error: error.message,
    });
  }
};
