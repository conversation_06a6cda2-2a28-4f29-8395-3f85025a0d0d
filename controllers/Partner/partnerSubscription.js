const Stripe = require("stripe");
const Subscription = require("../../models/Admin/subscription");
const PartnerSubscription = require("../../models/Partner/partnerSubscription");
const mongoose = require("mongoose");
const { createCheckoutLink } = require("../../util/stripe");
const { param, validationResult } = require("express-validator");

exports.stripe = new Stripe(process.env.STRIPE_SECRET, {
  apiVersion: "2024-06-20",
  typescript: true,
});

module.exports.getPartnerSubscriptionPaymentLink = [
  param("subscriptionId")
    .not()
    .isEmpty()
    .withMessage("subscriptionId Field is required"),
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    try {
      const { purchaseType, successUrl } = req.query;
      const findSubscription = await Subscription.findById({
        _id: req.params.subscriptionId,
        userType: "PARTNER",
        status: true,
      });
      // console.log("findSubscription", findSubscription);
      // console.log("purchaseType", purchaseType);
      if (findSubscription) {
        const planNameOn =
          purchaseType === "UPGRADE"
            ? `${findSubscription.planName} upgrade plan`
            : `${findSubscription.planName} subscription plan`;
        const checkOut = await createCheckoutLink(
          findSubscription.price,
          {
            userId: req.user._id,
            userType: req.user.userType,
            email: req.user?.email,
            userName: req.user?.fullName,
            subscriptionId: req.params.subscriptionId,
            month: findSubscription.timePeriod,
            isUpgrade: purchaseType === "UPGRADE" ? true : false,
            planName: planNameOn,

            purchaseType: "SUBSCRIPTION",
          },
          successUrl
        );

        res
          .status(200)
          .json({ url: checkOut, message: "Feteched successfully" });
      } else {
        res.status(400).json({ message: "Subscription not found" });
      }
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];

module.exports.getPartnerUpgradePaymentLink = [
  param("subscriptionId")
    .not()
    .isEmpty()
    .withMessage("subscriptionId Field is required"),
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    try {
      const { currentSubsciptionId, purchaseType, successUrl } = req.query;

      // Find the subscription in your local database
      const findSubscription = await Subscription.findOne({
        _id: req.params.subscriptionId,
        userType: "PARTNER",
        status: true,
      });
      const findCurrentSubscription = await PartnerSubscription.findOne({
        _id: currentSubsciptionId,
        isActive: true,
      });
      // console.log("findCurrentSubscription", findCurrentSubscription);
      if (!findSubscription) {
        return res.status(400).json({ message: "Subscription not found" });
      }
      // console.log("findSubscription", findSubscription);
      // Retrieve the current Stripe subscription ID
      const currentStripeSubscriptionId =
        findCurrentSubscription.stripeSubscriptionId; // Replace with your field name

      if (purchaseType === "UPGRADE") {
        // Fetch the existing Stripe subscription details
        const currentSubscription = await this.stripe.subscriptions.retrieve(
          currentStripeSubscriptionId
        );
        // console.log("currentSubscription", currentSubscription);
        // Define the new plan price ID (replace with your Stripe Price ID for Enterprise)
        const newPriceId = findSubscription.subscriptionPriceId; // Replace with the actual Price ID for Enterprise plan

        // Update the Stripe subscription to switch to the new plan
        const updatedSubscription = await this.stripe.subscriptions.update(
          currentStripeSubscriptionId,
          {
            metadata: {
              subscriptionId: req.params.subscriptionId,
              isUpgrade: "true",
              isCancel: "false",
            },
            cancel_at: null,
            items: [
              {
                id: currentSubscription.items.data[0].id, // Use the existing subscription item ID
                price: newPriceId, // New plan price ID
              },
            ],
            proration_behavior: "create_prorations", // Adjust billing for the new plan
          }
        );
        // console.log("updatedSubscription", updatedSubscription);

        return res.status(200).json({
          message: "Subscription upgraded successfully",
          updatedSubscription,
        });
      }

      // Default logic for fetching checkout link
      const planNameOn =
        purchaseType === "UPGRADE"
          ? `${findSubscription.planName} plan`
          : `${findSubscription.planName} plan`;

      const checkOut = await createCheckoutLink(
        findSubscription.price,
        {
          userId: req.user._id,
          userType: req.user.userType,
          email: req.user?.email,
          userName: req.user?.fullName,
          subscriptionId: req.params.subscriptionId,
          month: findSubscription.timePeriod,
          isUpgrade: purchaseType === "UPGRADE" ? true : false,
          planName: planNameOn,
          purchaseType: "UPGRADE",
        },
        successUrl
      );

      res.status(200).json({ url: checkOut, message: "Fetched successfully" });
    } catch (err) {
      console.error(err);
      res.status(400).json({ error: err.message });
    }
  },
];

module.exports.getAllSubscriptionPlans = [
  async (req, res) => {
    try {
      const data = await Subscription.find({
        userType: "PARTNER",
        status: true,
      });
      if (data) {
        res.status(201).json({ data });
      } else throw Error("No subscription plan found for user");
    } catch (err) {
      console.log(err, "error");
    }
  },
];

// Helper function to calculate the next valid cancellation date based on a 4-week notice
const calculateNextCancellationDate = (startDate) => {
  const date = new Date(startDate);
  // Ensure the date is a valid Date object
  if (isNaN(date.getTime())) {
    throw new Error("Invalid start date provided.");
  }
  // Add 28 days (4 weeks) to the start date
  date.setDate(date.getDate() + 28);
  // console.log("cal date", date);
  return date;
};

// Helper function to calculate the effective end date if the notice is not met
const getEffectiveEndDate = (currentEndDate) => {
  const effectiveEndDate = new Date(currentEndDate);
  // Add one month to the current period end date
  effectiveEndDate.setMonth(effectiveEndDate.getMonth() + 1);
  return effectiveEndDate;
};

module.exports.cancelSubscription = [
  async (req, res) => {
    try {
      // Retrieve partner subscription details
      const subscription = await PartnerSubscription.findOne({
        partnerId: req.user._id,
      });

      if (!subscription) {
        return res.status(404).json({ message: "Subscription not found" });
      }

      const stripeSubscription = await this.stripe.subscriptions.retrieve(
        subscription.stripeSubscriptionId
      );

      // console.log("stripeSubscription", stripeSubscription);
      const currentPeriodEnd = stripeSubscription.current_period_end;
      const currentPeriodStart = stripeSubscription.start_date;
      // console.log("currentPeriodEnd", currentPeriodEnd);
      console.log(
        "stripeSubscription.start_date",
        stripeSubscription.start_date
      );
      const { stripeSubscriptionId } = subscription;

      // Convert the current period end from seconds to a Date object
      const currentStartDate = new Date(currentPeriodStart * 1000);
      const currentEndDate = new Date(currentPeriodEnd * 1000);

      // Calculate the next possible cancellation date from the start date
      const nextCancellationDate =
        calculateNextCancellationDate(currentStartDate);

      // Log the dates to check their values
      // console.log("currentEndDate:", currentEndDate);
      // console.log("currentStartDate:", currentStartDate);
      // console.log("nextCancellationDate:", nextCancellationDate);

      const now = new Date();
      let effectiveCancellationDate;

      // Check if the cancellation notice period is respected
      if (now < nextCancellationDate) {
        // If within the notice period, cancellation will take effect at the current period end
        effectiveCancellationDate = currentEndDate;
      } else {
        // Otherwise, extend the cancellation to the next valid end date
        effectiveCancellationDate = getEffectiveEndDate(currentEndDate);
      }
      console.log(
        "first",
        Math.floor(effectiveCancellationDate.getTime() / 1000)
      );
      // Update the subscription in Stripe to cancel at the effective date
      await this.stripe.subscriptions.update(stripeSubscriptionId, {
        metadata: {
          isUpgrade: "false",
          isCancel: "true",
        },
        cancel_at: Math.floor(effectiveCancellationDate.getTime() / 1000), // Convert to Unix timestamp
      });

      // Update the local subscription record
      const updatePartner = await PartnerSubscription.findByIdAndUpdate(
        { _id: subscription._id },
        { isCancel: true, endAt: effectiveCancellationDate, canceledAt: now },
        { new: true }
      );
      // console.log("effectiveCancellationDate", effectiveCancellationDate);
      // console.log("updatePartner", updatePartner);
      // console.log("subscription", subscription);
      res.status(200).json({
        message: `Subscription will be cancelled effective on ${effectiveCancellationDate.toDateString()}`,
      });
    } catch (err) {
      console.error(err);
      res.status(500).json({ error: "Failed to cancel subscription" });
    }
  },
];

module.exports.getAPartnerSubscription = [
  async (req, res) => {
    try {
      const partnerSubscription = await PartnerSubscription.findOne({
        partnerId: req.user._id,
      }).populate("subscriptionId");
      // console.log("partnerSubscription", partnerSubscription);
      res.status(200).json(partnerSubscription);
    } catch (err) {
      console.log(err, "error");
    }
  },
];
