const Business = require("../../models/Partner/partnerBusiness");
const { query, body, validationResult } = require("express-validator");
const partnerNotification = require("../../models/Partner/partnerNotification");

module.exports.createBusiness = [
  body("name").not().isEmpty().withMessage("name Field is required"),
  body("location").not().isEmpty().withMessage("location Field is required"),
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    try {
      const partnerId = req.user._id;
      const createBusiness = await Business.create({ ...req.body, partnerId });
      if (createBusiness) {
        res.status(200).json({ data: createBusiness });
      } else throw Error("Something went wrong");
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];

module.exports.updateBusiness = [
  body("businessId")
    .not()
    .isEmpty()
    .withMessage("businessId Field is required"),
  async (req, res) => {
    try {
      const updateBusiness = await Business.findOneAndUpdate(
        { businessId: req.body.businessId },
        { ...req.body },
        { new: true }
      );
      if (updateBusiness) {
        res.status(200).json({ data: updateBusiness });
      } else throw Error("Something went wrong");
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];
module.exports.updatePartnerNotification = [
  body("notificationId")
    .not()
    .isEmpty()
    .withMessage("notificationId Field is required"),
  async (req, res) => {
    try {
      //console.log("req.body", req.body);
      const updatePartner = await partnerNotification.findByIdAndUpdate(
        { _id: req.body.notificationId },
        { ...req.body },
        { new: true }
      );
      if (updatePartner) {
        res.status(200).json({ data: updatePartner });
      } else throw Error("Something went wrong");
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];

module.exports.getBusiness = [
  // Validate _id query parameter
  query("_id").not().isEmpty().withMessage("_id field is required"),

  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { _id } = req.query;

      // Set the query object for finding the business
      const queryObj = { $or: [{ _id }, { partnerId: _id }] };

      // Fetch business and notifications concurrently
      const [findBusiness, getNotification] = await Promise.all([
        Business.findOne(queryObj).populate("partnerId", "-password"),
        partnerNotification.find({ partnerId: _id }).sort({ createdAt: -1 }),
      ]);

      // Check if business data was found
      if (!findBusiness) {
        return res.status(404).json({ error: "No business data found" });
      }

      // Merge the results
      const finalData = {
        ...findBusiness.toObject(), // Convert Mongoose document to plain object
        notifications: getNotification,
      };

      res.status(200).json({ data: finalData });
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },
];
