const Ads = require("../../models/Partner/partnerAds");
const ExternalAds = require("../../models/Partner/externanAd");
const { query, body, validationResult } = require("express-validator");
const { sendEmail } = require("../../util/sendEmail");
const moment = require("moment");
const { Error, default: mongoose } = require("mongoose");
const Analytics = require("../../models/Partner/analytics");
const Invitation = require("../../models/User/invitations");
const Business = require("../../models/Partner/partnerBusiness");
module.exports.createAds = [
  body("name").not().isEmpty().withMessage("name Field is required"),
  body("releaseDate")
    .not()
    .isEmpty()
    .withMessage("releaseDate Field is required"),
  body("adType").not().isEmpty().withMessage("adType Field is required"),
  async (req, res) => {
    let errors = validationResult(req).array();
    // check if adType is not suggestion then check validation for adTitle & adBody
    if (req.body.adType != "Suggestion") {
      let errorMessageObj = {
        value: "",
        msg: "",
        param: "",
        location: "body",
      };

      if (!("title" in req.body) || req.body?.title?.trim() == "")
        errors.push({
          ...errorMessageObj,
          msg: "title Field is required",
          param: "title",
        });

      if (!("body" in req.body) || req.body?.body?.trim() == "")
        errors.push({
          ...errorMessageObj,
          msg: "body Field is required",
          param: "body",
        });
    }

    if (errors.length > 0) {
      return res.status(400).json({ errors: errors });
    }
    try {
      const partnerId = req.user._id;
      const createAds = await Ads.create({ ...req.body, partnerId });
      if (createAds) {
        res.status(200).json({ data: createAds });
      } else throw Error("Something went wrong");
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];
const getAdsAnalytics = async (_id, actionType, type) => {
  let obj = {};
  if (type === "INTERNAL") {
    obj["adId"] = _id;
  }
  if (type === "EXTERNAL") {
    obj["extAdId"] = _id;
  }
  const internalStats = await Analytics.aggregate([
    {
      $match: {
        actionType,
        adId: mongoose.Types.ObjectId(_id),
        deleted: { $ne: true },
      },
    },
    {
      $group: {
        _id: {
          hour: { $hour: "$actionTime" },
          minute: { $minute: "$actionTime" },
          second: { $second: "$actionTime" },
        },
        count: { $sum: 1 },
      },
    },
    {
      $project: {
        _id: 0,
        time: {
          $concat: [
            { $toString: "$_id.hour" },
            ":",
            { $toString: "$_id.minute" },
            ":",
            { $toString: "$_id.second" },
          ],
        },
        count: 1,
      },
    },
    {
      $sort: { time: 1 },
    },
  ]);
  if (internalStats) {
    return internalStats;
  } else false;
};
async function getInvitation(businessId, obj, adId) {
  const result = await Invitation.aggregate([
    {
      $match: {
        businessId: new mongoose.Types.ObjectId(businessId),
        deleted: { $ne: true },
        ...obj,
        adId: new mongoose.Types.ObjectId(adId),
      },
    },
    {
      $addFields: {
        hour: {
          $toInt: {
            $substr: ["$time", 10, 2], // Extracts "15" from "TimeOfDay(15:36)"
          },
        },
        minute: {
          $toInt: {
            $substr: ["$time", 13, 2], // Extracts "36" from "TimeOfDay(15:36)"
          },
        },
      },
    },
    {
      $group: {
        _id: {
          hour: "$hour",
          minute: "$minute",
        },
        count: { $sum: 1 },
      },
    },
    {
      $project: {
        _id: 0,
        time: {
          $concat: [
            { $toString: "$_id.hour" },
            ":",
            { $toString: "$_id.minute" },
          ],
        },
        count: 1,
      },
    },
    {
      $sort: { time: 1 },
    },
  ]);

  return result;
}

module.exports.getAdvtertisementStats = [
  query("_id").not().isEmpty().withMessage("_id Field is required"),
  query("type").not().isEmpty().withMessage("type Field is required"),
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { _id, type } = req.query;

      let obj = {};
      const findAd = await Ads.findOne({ _id });
      const findBusiness = await Business.findOne({
        partnerId: findAd.partnerId,
      });
      if (!findBusiness) {
        throw new Error("Business not found.");
      }
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const totalInvitation = await getInvitation(
        findBusiness._id,
        obj,
        findAd._id
      );

      const internalClickStats = await getAdsAnalytics(_id, "CLICK", type);
      const internalImpressionStats = await getAdsAnalytics(
        _id,
        "IMPRESSION",
        type
      );
      if (internalClickStats || internalImpressionStats) {
        res.status(200).json({
          data: {
            internalClickStats,
            internalImpressionStats,
            invitesStats: totalInvitation,
          },
        });
      } else throw Error("No data found");
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];
module.exports.getSingleAds = [
  query("_id").not().isEmpty().withMessage("_id Field is required"),
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    try {
      const { _id } = req.query;
      const data = await Ads.findById({ _id });
      if (data) {
        res.status(200).json({ data });
      } else throw Error("data not found");
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];
module.exports.getSingleRequestedAds = [
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { id } = req.params;
      const data = await Ads.findById({ _id: id });
      //console.log("data", data);
      if (data) {
        if (
          data.status === "Requested" ||
          "Rejected" ||
          "Approved" ||
          "Running"
        ) {
          res.status(200).json({ data });
        } else {
          res.status(200).json({ message: "no requested data found" });
        }
      } else {
        throw new Error("data not found");
      }
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];
module.exports.updateAds = [
  body("businessId")
    .not()
    .isEmpty()
    .withMessage("businessId Field is required"),
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    try {
      const { businessId } = req.body;
      const updateBusiness = await Ads.findOneAndUpdate(
        { businessId },
        { ...req.body },
        { new: true }
      );
      if (updateBusiness) {
        res.status(200).json({ data: updateBusiness });
      } else throw Error("Something went wrong");
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];
module.exports.updateAdsStatus = [
  body("adId").not().isEmpty().withMessage("businessId Field is required"),
  body("status").not().isEmpty().withMessage("partnerId Field is required"),
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    try {
      const { adId, status } = req.body;
      const updateStatus = await Ads.findOneAndUpdate(
        { _id: adId },
        { ...req.body },
        { status: status }
      );
      if (updateStatus) {
        res.status(200).json({ data: updateStatus });
      } else throw Error("Something went wrong");
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];
module.exports.deleteAd = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  try {
    const deleteAd = await Ads.findByIdAndDelete({
      _id: req.params.id,
    }).populate("partnerId");
    if (!deleteAd) {
      throw new Error(`No partner ad found with this Id: ${req.params.id}`);
    }
    res.status(200).json({ message: "Ad Deleted Successfully" });
  } catch (err) {
    let error = err.message;
    res.status(400).json({ error: error });
  }
};

module.exports.getAds = [
  query("_id").not().isEmpty().withMessage("_id Field is required"),
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { _id } = req.query;
      const findBusiness = await Ads.findOne({ _id });
      if (findBusiness) {
        res.status(200).json({ data: findBusiness });
      } else throw new Error("No data found");
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];

function generateEmailText(data) {
  return `
You have received new ad request,
Name: ${data.legalRepresentative.firstName} ${data.legalRepresentative.lastName},
Email: ${data.legalRepresentative.email},
Title: ${data.title},
Release Date: ${data.releaseDate},
Company Name: ${data.companyName},
Address: ${data.address},
Business Email: ${data.businessEmail},
Business Mobile: ${data.businessMobile},
Category: ${data.category},
Ad Type: ${data.adType}
`;
}

module.exports.createExternalAds = [
  body("body").not().isEmpty().withMessage("body Field is required"),
  body("title").not().isEmpty().withMessage("title Field is required"),
  body("releaseDate")
    .not()
    .isEmpty()
    .withMessage("releaseDate Field is required"),
  body("legalRepresentative")
    .not()
    .isEmpty()
    .withMessage("legalRepresentative object field is required"),
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    try {
      const createAds = await ExternalAds.create({ ...req.body });
      if (createAds) {
        const mailOptions = {
          from: '"NETME Partner" <' + process.env.EMAIL + ">",
          to: "<EMAIL>",
          subject: "New ad request",
          text: generateEmailText(createAds),
        };
        await sendEmail(mailOptions);
        res.status(200).json({ data: createAds });
      } else throw Error("Something went wrong");
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];

module.exports.getAllAds = async (req, res) => {
  try {
    let page = parseInt(req.query.page ? req.query.page : 1);
    let limit = parseInt(req.query.limit ? req.query.limit : 100);
    let skipValue = (page - 1) * limit;
    let obj = {};
    let filterObj = {};
    let { status, search, releaseStartDate, releaseEndDate } = req.query;

    const currentDate = new Date(moment().format("YYYY-MM-DD"));
    if (status === "History" || status.includes("History")) {
      obj.userType = { $ne: "ADMIN" };
      obj.userType = { $eq: "PARTNER" };
      obj = {
        ...obj,

        $or: [
          { releaseDate: { $lt: currentDate } },
          { status: "Ended" },
          { status: "Deleted" },
          { status: "Cancelled" },
          { status: "Rejected" },
          { deleted: true },
        ],
      };
    }

    if (status === "Overview" || status.includes("Overview")) {
      obj.releaseDate = { $gte: currentDate };
      obj.deleted = false;
      obj.status = { $ne: "Deleted" };
      obj.userType = { $ne: "ADMIN" };
      obj.userType = { $eq: "PARTNER" };
    }

    if (
      status === "Requested" ||
      status === "Rejected" ||
      status === "Approved" ||
      typeof status === "object"
    ) {
      if (typeof status === "object") {
        obj.status = { $in: status };
        filterObj.status = { $in: status };
      } else {
        obj.status = { $in: [status] };
        filterObj.status = { $in: [status] };
      }
    }

    if (releaseStartDate) {
      obj.releaseDate = {
        $gte: new Date(releaseStartDate),
      };
      filterObj.releaseDate = {
        $gte: new Date(releaseStartDate),
      };
    }
    if (releaseEndDate) {
      obj.releaseDate = {
        $lte: new Date(moment(releaseEndDate).endOf("day")),
      };
      filterObj.releaseDate = {
        $lte: new Date(moment(releaseEndDate).endOf("day")),
      };
    }
    if (releaseStartDate && releaseEndDate) {
      obj.releaseDate = {
        $gte: new Date(releaseStartDate),
        $lte: new Date(moment(releaseEndDate).endOf("day")),
      };
      filterObj.releaseDate = {
        $gte: new Date(releaseStartDate),
        $lte: new Date(moment(releaseEndDate).endOf("day")),
      };
    }

    // if (search) {
    //   obj.name = { $regex: search, $options: "i" };
    //   filterObj.name = { $regex: search, $options: "i" };
    // }
    if (search) {
      const searchFields = [
        "name",
        "title",
        "body",
        "status",
        "location",
        "adType",
        "cities",
        "partnerBusiness.name",
      ];
      const searchCondition = {
        $or: searchFields.map((field) => ({
          [field]: { $regex: search, $options: "i" },
        })),
      };
      obj = { ...obj, ...searchCondition };
    }
    const data = await Ads.aggregate([
      {
        $lookup: {
          from: "partners",
          localField: "partnerId",
          foreignField: "_id",
          as: "partnerId",
        },
      },
      {
        $unwind: {
          path: "$partnerId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "partnerbusinesses", // The collection name for partnerBusiness
          localField: "partnerId._id", // The _id from the partner collection
          foreignField: "partnerId", // The partnerId field in partnerBusiness
          as: "partnerBusiness",
        },
      },
      {
        $unwind: {
          path: "$partnerBusiness",
          preserveNullAndEmptyArrays: true, // Allow empty business information
        },
      },
      {
        $match: {
          ...obj,
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
      {
        $project: {
          "partnerId.password": 0, // Exclude the password field
          // You can include any fields from partnerId and partnerBusiness
        },
      },
      {
        $facet: {
          data: [{ $skip: skipValue }, { $limit: limit }],
          totalCount: [
            { $group: { _id: null, count: { $sum: 1 } } },
            { $project: { _id: 0, count: 1 } },
          ],
        },
      },
    ]);

    const stats = await Ads.aggregate([
      {
        $match: { ...filterObj },
      },
      {
        $group: {
          _id: null,
          overView: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ["$deleted", false] },
                    { $ne: ["$status", "Deleted"] },
                    { $ne: ["$userType", "ADMIN"] },
                    { $eq: ["$userType", "PARTNER"] },
                    { $gte: ["$releaseDate", currentDate] },
                  ],
                },
                1,
                0,
              ],
            },
          },
          historyCount: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $lt: ["$releaseDate", currentDate] }, // Release date before current date
                    { $ne: ["$userType", "ADMIN"] }, // User type not "ADMIN"
                    { $eq: ["$userType", "PARTNER"] }, // User type is "PARTNER"
                    {
                      $or: [
                        // OR any of these statuses for historical ads
                        { $eq: ["$status", "Ended"] }, // Ended status
                        { $eq: ["$status", "Approved"] }, // Ended status
                        { $eq: ["$status", "Requested"] }, // Ended status
                        { $eq: ["$status", "WithDrawed"] }, // Ended status
                        { $eq: ["$status", "Rejected"] }, // Ended status
                        { $eq: ["$status", "Cancelled"] }, // Ended status
                        // Add other historical status conditions here (e.g., Deleted, Cancelled)
                      ],
                    },
                  ],
                },
                1,
                0,
              ],
            },
          },
          // historyCount: {
          //   $sum: {
          //     $cond: [
          //       {
          //         $or: [
          //           { $lt: ["$releaseDate", currentDate] },
          //           { $ne: ["$userType", "ADMIN"] },
          //           { $eq: ["$userType", "PARTNER"] },
          //           { $eq: ["$status", "Ended"] },
          //         ],
          //       },
          //       1,
          //       0,
          //     ],
          //   },
          // },
        },
      },
      {
        $project: {
          _id: 0,
          overView: 1,
          historyCount: 1,
        },
      },
    ]);
    const result = stats[0];
    const overView = result ? result.overView : 0;
    const historyCount = result ? result.historyCount : 0;
    res.status(200).json({
      data: data[0]["data"],
      overViewCount: overView,
      historyCount,
      totalPage: Math.ceil(
        data[0].totalCount.length > 0 ? data[0].totalCount[0].count / limit : 0
      ),
      totalData:
        data[0].totalCount.length > 0 ? data[0].totalCount[0].count : 0,
      perPage: limit,
      currentPage: page,
    });
  } catch (err) {
    console.log(err);
    res.status(400).json({ error: "Something Went Wrong" });
  }
};
