const Stamp = require("../../models/User/stamp");
const Voucher = require("../../models/Admin/voucher");
const { body, validationResult, query } = require("express-validator");
const generateUniqueRandomNumber = require("../../util/generateUniqueNumber");
const mongoose = require("mongoose");
const Business = require("../../models/Partner/partnerBusiness");
module.exports.createStamp = [
  body("businessId")
    .not()
    .isEmpty()
    .withMessage("businessId Field is required"),
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    try {
      const { businessId } = req.body;
      const business = await Business.findById({ _id: businessId });
      if (!business) {
        return res.status(400).json({
          error: `No business found with this Business Id: ${businessId}`,
        });
      }
      // Get the start and end of the current day
      const now = new Date();
      const startOfDay = new Date(now.setHours(0, 0, 0, 0));
      const endOfDay = new Date(now.setHours(23, 59, 59, 999));

      // Check if a stamp already exists for the user and business today
      const stampCreateFive = await Stamp.find({
        userId: req.user._id,
        businessId,
      }).populate("businessId");
      //console.log("stampCreateFive", stampCreateFive);
      // Check if a stamp already exists for the user and business today
      const existingStamp = await Stamp.findOne({
        userId: req.user._id,
        businessId,
        createdAt: {
          $gte: startOfDay,
          $lte: endOfDay,
        },
      });

      if (stampCreateFive.length === 5) {
        return res.status(400).json({
          error: `You have already scanned 5 stamps for this business: ${stampCreateFive[0]?.businessId?.name}`,
        });
      }
      // if (existingStamp) {
      //   return res.status(400).json({ error: 'You can only create one stamp per business per day.' });
      // }
      const createStamp = await Stamp.create({
        ...req.body,
        userId: req.user._id,
      });
      await createStamp.populate("businessId");
      if (createStamp) {
        const totalStampCount = await Stamp.countDocuments({
          ...req.body,
          userId: req.user._id,
        });
        let createVoucher;

        if (totalStampCount % 1 === 0) {
          // const expiredAt = new Date();
          // expiredAt.setHours(expiredAt.getHours() + 2);
          const expiredAt = new Date();
          expiredAt.setMonth(expiredAt.getMonth() + 1);
          const uniqueRandomNumber = await generateUniqueRandomNumber();
          createVoucher = await Voucher.create({
            name: "voucher",
            code: uniqueRandomNumber,
            percentage: 10,
            userType: "USER",
            userId: req.user._id,
            active: false,
            stampId: createStamp._id,
            businessId: createStamp.businessId,
            expiryDate: expiredAt,
          });
        }
        res
          .status(200)
          .json({ data: createStamp, totalStampCount, createVoucher });
      }
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];

module.exports.getAllUserStamps = [
  async (req, res) => {
    try {
      const pipeline = [
        {
          $group: {
            _id: {
              userId: "$userId",
              businessId: "$businessId",
            },

            count: { $sum: 1 },
            stampId: { $first: "$_id" },
          },
        },
        {
          $lookup: {
            from: "partnerbusinesses",
            localField: "_id.businessId",
            foreignField: "_id",
            as: "businessDetails",
          },
        },
        {
          $lookup: {
            from: "vouchers",
            localField: "_id.userId",
            foreignField: "userId",
            as: "vouchers",
          },
        },

        {
          $project: {
            _id: "$stampId",
            userId: "$_id.userId",
            businessId: "$_id.businessId",
            count: "$count",

            businessDetails: { $arrayElemAt: ["$businessDetails", 0] },
            vouchers: "$vouchers", // Include voucher data in the result
          },
        },
        {
          $match: {
            userId: mongoose.Types.ObjectId(req.user._id),
          },
        },
      ];

      const findStamp = await Stamp.aggregate(pipeline);
      //console.log("findStamp", findStamp);
      if (findStamp) {
        res.status(200).json({ data: findStamp, count: findStamp.length });
      }
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error });
    }
  },
];
