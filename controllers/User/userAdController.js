const PartnerAds = require("../../models/Partner/partnerAds");
const ExternalAds = require("../../models/Partner/externanAd");
const User = require("../../models/User/User");

// module.exports.getAllAds = async (req, res) => {
//   try {
//     const page = parseInt(req.query.page) || 1;
//     const limit = parseInt(req.query.limit) || 10;
//     const skip = (page - 1) * limit;
//     const { city } = req.query;
//     const today = new Date();
//     today.setHours(0, 0, 0, 0);
//     console.log("today", today);
//     const externalAdsQuery = {
//       releaseDate: {
//         $gte: today,
//         $lt: new Date(today.getTime() + 24 * 60 * 60 * 1000),
//       },
//       city: new RegExp(city, "i"),
//     };
//     const partnerAdsQuery = {
//       releaseDate: {
//         $gte: today,
//         $lt: new Date(today.getTime() + 24 * 60 * 60 * 1000),
//       },
//       cities: {
//         $in: [new RegExp(city, "i")],
//       },
//     };
//     console.log("partnerAdsQuery", partnerAdsQuery);
//     console.log("externalAdsQuery", externalAdsQuery);

//     const externalAds = await ExternalAds.find(externalAdsQuery)
//       .skip(skip)
//       .limit(limit)
//       .lean();
//     const partnerAds = await PartnerAds.aggregate([
//       {
//         $match: partnerAdsQuery,
//       },

//       {
//         $lookup: {
//           from: "partners",
//           localField: "partnerId",
//           foreignField: "_id",
//           as: "partnerId",
//         },
//       },
//       {
//         $unwind: {
//           path: "$partnerId",
//           preserveNullAndEmptyArrays: true,
//         },
//       },
//       {
//         $lookup: {
//           from: "partnerbusinesses",
//           localField: "partnerId._id",
//           foreignField: "partnerId",
//           as: "businessDetails",
//         },
//       },
//       {
//         $unwind: {
//           path: "$businessDetails",
//           preserveNullAndEmptyArrays: true,
//         },
//       },
//       {
//         $project: {
//           "partnerId.password": 0, // Exclude the password field
//         },
//       },
//       {
//         $skip: (page - 1) * limit,
//       },
//       {
//         $limit: limit,
//       },
//     ]);

//     const allAds = [...externalAds, ...partnerAds];
//     const totalExternalAds = await ExternalAds.countDocuments(externalAdsQuery);
//     const totalPartnerAds = await PartnerAds.countDocuments(partnerAdsQuery);
//     const totalCount = totalExternalAds + totalPartnerAds;

//     res.json({
//       data: allAds,
//       total: totalCount,
//       limit: limit,
//       totalPage: Math.ceil(totalCount / limit),
//     });
//   } catch (error) {
//     console.error(error);
//     res.status(500).json({ error: "Internal Server Error" });
//   }
// };

module.exports.getAllAds = async (req, res) => {
  try {
    const { city, latitude, longitude } = req.query;
    const userId = req.user._id;
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }
    if (!!user.isPremium) {
      return res.json({
        data: [],
        total: 0,
        message: "No Ads For Premium Users",
      });
    }
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const userAge = calculateAge(user?.dob);
    // console.log("user", user);
    // Initialize daily ad tracking if it doesn't exist or is from a previous day
    if (!user?.dailyAdTracking || user?.dailyAdTracking?.date < today) {
      user.dailyAdTracking = {
        date: today,
        count: 0,
        viewedAds: [],
      };
    }
    // console.log("new Date(0)", new Date(0));
    const lastAdTime = user.lastAdTime || new Date(0);
    const timeSinceLastAd = now - lastAdTime;
    // console.log("lastAdTime", lastAdTime);
    // if (timeSinceLastAd < 30000) {
    //   //3600000
    //   return res.json({
    //     data: [],
    //     total: 0,
    //     message: "Come back after 1hr",
    //   });
    // }
    // // Check if the user has already seen 3 ads today
    // if (user?.dailyAdTracking?.count >= 10) {
    //   return res.json({
    //     data: [],
    //     total: 0,
    //     message: "Daily ad limit reached",
    //   });
    // }

    // Function to calculate age
    function calculateAge(birthday) {
      const ageDifMs = Date.now() - birthday?.getTime();
      const ageDate = new Date(ageDifMs);
      return Math.abs(ageDate.getUTCFullYear() - 1970);
    }
    // console.log("userAge", userAge);
    // Function to check if it's a weekday
    function isWeekday(date) {
      const day = date.getDay();
      return day >= 1 && day <= 5; // Monday is 1, Friday is 5
    }

    // Query for external ads
    const externalAdsQuery = {
      releaseDate: {
        $gte: today,
        $lt: new Date(today.getTime() + 24 * 60 * 60 * 1000),
      },
      cities: {
        $in: [new RegExp(city, "i")],
      },
      status: "Approved",
      ageRange: {
        $gte: userAge,
        $lte: userAge,
      },
      _id: { $nin: user.dailyAdTracking.viewedAds },
    };

    // Query for partner ads
    const partnerAdsQuery = {
      releaseDate: {
        $gte: today,
        $lt: new Date(today.getTime() + 24 * 60 * 60 * 1000),
      },
      cities: {
        $in: [new RegExp(city, "i")],
      },
      status: "Approved",
      _id: { $nin: user.dailyAdTracking.viewedAds },
    };

    // Add day of week filter for partner ads
    if (isWeekday(now)) {
      partnerAdsQuery.adType = { $in: ["Feed", "Suggestion"] };
    }

    // console.log("partnerAdsQuery", partnerAdsQuery);
    // console.log("externalAdsQuery", externalAdsQuery);

    const externalAds = await ExternalAds.find(externalAdsQuery).lean();

    const partnerAds = await PartnerAds.aggregate([
      {
        $match: partnerAdsQuery,
      },

      {
        $lookup: {
          from: "partners",
          localField: "partnerId",
          foreignField: "_id",
          as: "partnerId",
        },
      },
      {
        $unwind: {
          path: "$partnerId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "partnerbusinesses",
          localField: "partnerId._id",
          foreignField: "partnerId",
          as: "businessId",
        },
      },
      {
        $unwind: {
          path: "$businessId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          "partnerId.password": 0, // Exclude the password field
        },
      },
    ]);

    // Combine and shuffle ads
    let allAds = [...externalAds, ...partnerAds];
    allAds = allAds.sort(() => Math.random() - 0.5);

    // Filter ads based on user's last ad time // Old
    // const filteredAds = allAds.filter((ad) => {
    //   // console.log("ad", ad);
    //   // Check for 10km radius (assuming latitude and longitude are provided)
    //   if (ad.adType === "Feed" && latitude && longitude) {
    //     const distance = calculateDistance(
    //       latitude,
    //       longitude,
    //       ad.location.coordinates[0],
    //       ad.location.coordinates[1]
    //     );
    //     console.log("distance", distance);
    //     if (distance > 10) {
    //       console.log("I ");
    //       return false;
    //     }
    //   }

    //   return true;
    // });

    // Filter ads based on user's last ad time // New
    const filteredAds = allAds.filter((ad) => {
      if (ad.adType === "Feed" && latitude && longitude) {
        const distance = calculateDistance(
          latitude,
          longitude,
          ad.location.coordinates[0],
          ad.location.coordinates[1]
        );
        ad.businessId.distance = distance; // Add distance field for Feed ads
        ad.businessId.scope = distance <= 10 ? "Within Range" : "Out of Range"; // Add scope field
        if (distance > 10) {
          return false;
        }
      }
      return true;
    });

    // Limit to 3 ads per day per user
    // Limit to remaining allowed ads for the day
    // console.log("user.dailyAdTracking", user.dailyAdTracking);
    const remainingAds = 100 - user.dailyAdTracking.count;
    // console.log("count", user.dailyAdTracking.count);
    // console.log("remainingAds", remainingAds);
    const adsToShow = filteredAds.slice(0, remainingAds);
    // console.log("adsToShow", adsToShow);
    // Update user's last ad time and daily tracking
    // console.log("adsToShow.length", adsToShow.length);
    // console.log("[adsToShow[0]].length", [adsToShow[0]].length);
    if (adsToShow.length > 0) {
      user.lastAdTime = now;
      user.dailyAdTracking.count += [adsToShow[0]].length;
      // user.dailyAdTracking.viewedAds.push(
      //   ...[adsToShow[0]].map((ad) => ad._id)
      // );
      await user.save();
    }

    // Format the response with the first ad
    let responseData = adsToShow[0] ? [adsToShow[0]] : [];

    // Transform the response data to include distance and scope only for Feed ads
    responseData = responseData.map((ad) => {
      if (ad.adType === "Feed") {
        return {
          ...ad,
          distance: ad.distance,
          scope: ad.scope,
        };
      }
      return ad;
    });
    // console.log("responseData", responseData);
    res.json({
      data: responseData,
      total: adsToShow.length,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// Helper function to calculate distance between two points
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Radius of the Earth in km
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLon = ((lon2 - lon1) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  return distance;
}
