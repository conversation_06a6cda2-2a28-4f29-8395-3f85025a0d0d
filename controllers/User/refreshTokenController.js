const { body, validationResult } = require("express-validator");
const {
  revokeRefreshToken,
  refreshAccessToken,
} = require("../../middleware/createToken");

module.exports.refreshToken = [
  body("refreshToken").not().isEmpty().withMessage("Refresh token is required"),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { refreshToken } = req.body;
      const result = await refreshAccessToken(refreshToken);

      if (!result) {
        return res
          .status(401)
          .json({ error: "Invalid or expired refresh token" });
      }

      res.status(200).json({
        accessToken: result.accessToken,
        refreshToken: result.refreshToken,
        user: result.user,
      });
    } catch (err) {
      console.error("Error in refresh token route:", err);
      res.status(500).json({ error: "Server error" });
    }
  },
];

module.exports.revokeRefreshToken = [
  body("refreshToken").not().isEmpty().withMessage("Refresh token is required"),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { refreshToken } = req.body;
      const success = revokeRefreshToken(refreshToken);

      if (!success) {
        return res.status(400).json({ error: "Failed to revoke token" });
      }

      res.status(200).json({ message: "Token revoked successfully" });
    } catch (err) {
      console.error("Error in revoke token route:", err);
      res.status(500).json({ error: "Server error" });
    }
  },
];
