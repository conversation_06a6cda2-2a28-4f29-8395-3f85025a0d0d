const Stripe = require("stripe");
const Partner = require("../../models/Partner/Partner");
const Stamp = require("../../models/User/stamp");
const Invitations = require("../../models/User/invitations");
const PartnerBusiness = require("../../models/Partner/partnerBusiness");
const PartnerAds = require("../../models/Partner/partnerAds");
const ExternalAds = require("../../models/Partner/externanAd");
const { body, validationResult, query } = require("express-validator");
const moment = require("moment");
const { sendEmail } = require("../../util/sendEmail");
const { default: mongoose } = require("mongoose");
const Business = require("../../models/Partner/partnerBusiness");
const bcrypt = require("bcryptjs");
const PartnerSubscription = require("../../models/Partner/partnerSubscription");
const { calculateExpiryTime } = require("../../util/calculateExpiryTime");
exports.stripe = new Stripe(process.env.STRIPE_SECRET, {
  apiVersion: "2024-06-20",
  typescript: true,
});
module.exports.getAllPartners = async (req, res) => {
  try {
    let sort = req.query.sort;
    let page = parseInt(req.query.page ? req.query.page : 1);
    let limit = parseInt(req.query.limit ? req.query.limit : 100);
    // //console.log("limit", limit);
    let skipValue = (page - 1) * limit;
    let obj = {};
    let { status, search } = req.query;
    // //console.log("search", search);
    if (status) {
      obj["partnerId.status"] = status;
    }

    if (search) {
      obj["$or"] = [
        { "partnerId.fullName": { $regex: search, $options: "i" } },
        { "partnerId.email": { $regex: search, $options: "i" } },
        { name: { $regex: search, $options: "i" } },
      ];
    }
    // //console.log("obj", obj);
    const data = await PartnerBusiness.aggregate([
      {
        $lookup: {
          from: "partners",
          localField: "partnerId",
          foreignField: "_id",
          as: "partnerId",
        },
      },
      {
        $unwind: {
          path: "$partnerId",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          ...obj,
          "partnerId.deleted": false,
        },
      },
      {
        $lookup: {
          from: "partnersubscriptions",
          localField: "partnerId._id",
          foreignField: "partnerId",
          as: "partnerSubscriptions",
        },
      },
      {
        $unwind: {
          path: "$partnerSubscriptions",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "subscriptions",
          localField: "partnerSubscriptions.subscriptionId",
          foreignField: "_id",
          as: "subscription",
        },
      },
      {
        $unwind: {
          path: "$subscription",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          nameLower: { $toLower: "$name" },
        },
      },
      {
        $project: {
          "partnerId.password": 0, // Exclude the password field
        },
      },
      {
        $sort: sort ? { createdAt: -1 } : { nameLower: 1 },
      },
      {
        $facet: {
          data: [{ $skip: skipValue }, { $limit: limit }],
          totalCount: [
            { $group: { _id: null, count: { $sum: 1 } } },
            { $project: { _id: 0, count: 1 } },
          ],
        },
      },
    ]);

    const stats = await Partner.aggregate([
      {
        $group: {
          _id: null,
          totalActive: {
            $sum: { $cond: [{ $eq: ["$status", "Active"] }, 1, 0] },
          },
          totalRequested: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ["$status", "Requested"] },
                    { $eq: ["$deleted", false] },
                  ],
                },
                1,
                0,
              ],
            },
          },
          totalBlacklisted: {
            $sum: { $cond: [{ $eq: ["$status", "Blacklisted"] }, 1, 0] },
          },
          totalDeleted: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ["$status", "Deleted"] },
                    { $eq: ["$deleted", false] },
                  ],
                },
                1,
                0,
              ],
            },
          },
        },
      },
    ]);

    const finalCount = stats[0];
    if (data && data.length > 0) {
      const totalActive = finalCount ? finalCount.totalActive : 0;
      const totalBlacklisted = finalCount ? finalCount.totalBlacklisted : 0;
      const totalDeleted = finalCount ? finalCount.totalDeleted : 0;
      const totalRequested = finalCount ? finalCount.totalRequested : 0;
      const totalPartnerCount =
        totalActive + totalBlacklisted + totalDeleted + totalRequested;
      res.status(200).json({
        data: data[0]["data"],
        perPage: limit,
        ...finalCount,
        totalPage: Math.ceil(
          data[0].totalCount.length > 0
            ? data[0].totalCount[0].count / limit
            : 0
        ),
        totalPartnerCount,
        totalCount:
          data[0].totalCount.length > 0 ? data[0].totalCount[0].count : 0,
      });
    } else {
      res.status(200).json({ data: [], totalCount: 0 });
    }
  } catch (err) {
    console.log(err);
    res.status(400).json({ error: "Something Went Wrong" });
  }
};

function generateRandomPassword() {
  const digits = "0********9";
  let password = "";
  for (let i = 0; i < 6; i++) {
    const randomIndex = Math.floor(Math.random() * digits.length);
    password += digits[randomIndex];
  }
  return password;
}
module.exports.updatePartner = [
  body("partnerId").not().isEmpty().withMessage("partnerId Field is required"),
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    try {
      if (req.body.deleted) {
        req.body.status = "Deleted";
      }
      const randomPassword = generateRandomPassword();
      const hashedPassword = await bcrypt.hash(randomPassword, 10);
      req.body.password = hashedPassword;
      const partnerSubscriptionData = await PartnerSubscription.findOne({
        partnerId: req.body.partnerId,
      });
      let data = await Partner.findByIdAndUpdate(
        { _id: req.body.partnerId },
        {
          ...req.body,
        },
        { new: true }
      );
      const mailOptions = {
        from: '"NETME Partner" <' + process.env.EMAIL + ">",
        to: data.email,
      };
      if (req.body.status === "Active") {
        const now = new Date();
        const expireAt = calculateExpiryTime(
          partnerSubscriptionData.timePeriod
        );
        const updateExpireData = {
          expireAt,
          startAt: now,
          isActive: true,
        };
        const updatePartner = {
          isPartnerActive: true,
        };

        // Update Stripe subscription expiry
        const updatePayment = await this.stripe.subscriptions.update(
          partnerSubscriptionData.stripeSubscriptionId,
          {
            cancel_at_period_end: false, // Continue the subscription
            trial_end: "now", // End trial immediately, starting the subscription
            proration_behavior: "none", // No proration of the charge
            metadata: {
              isActive: true, // Metadata key-value pairs must be strings
            },
          }
        );
        if (updatePayment.status === "active") {
          const afterUpdate = await PartnerSubscription.findOneAndUpdate(
            { partnerId: req.body.partnerId },
            { $set: updateExpireData },
            { new: true }
          );
          const afterPartnerBusinessUpdate =
            await PartnerBusiness.findOneAndUpdate(
              { partnerId: req.body.partnerId },
              { $set: updatePartner },
              { new: true }
            );
        }
        await Partner.findByIdAndUpdate(
          { _id: req.body.partnerId },
          { isResetPassword: true },
          { new: true }
        );
        mailOptions.subject =
          "Welcome to NETME! Your Partner Access Information";
        (mailOptions.text = `<div style="font-family: Arial, sans-serif; color: #333; background-color: #8CC9E9; padding: 20px; max-width: 600px; margin: 20px auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <div style="background-color: #333; color: white; padding: 10px 15px; border-radius: 8px 8px 0 0; text-align: center;">
                <h1 style="margin: 0; font-size: 24px;">Joining Forces: Welcome Aboard!</h1>
            </div>
            <div style="padding: 20px; background-color: white; border-radius: 0 0 8px 8px;">
                <p>Dear ${data.fullName},</p>
                <p>We are thrilled to inform you that your request to become a NETME Partner has been approved! Welcome aboard.</p>
                <p>To get started, please find your one-time password (OTP) and the link to your personal Partner Backpanel below. This will grant you access to your dashboard, where you can manage your partnership and explore the features we offer.</p>
                <p>One-Time Password (OTP): ${randomPassword}</p>
                <p>Partner Backpanel URL: <a href="https://partner.netme.eu/login?verification=${randomPassword}">https://partner.netme.eu/login?verification=${randomPassword}</a> </p>
                <p>Please log in using the OTP provided. After your initial login, you will be prompted to set a new password for future access.</p>
                <p>Additionally, you can manage your partnership on-the-go by downloading our NETME Partner app from the Apple App Store or Google Play Store:</p>
                <p>[Apple App Store Link]</p>
                <p>[Google Play Store Link]</p>
                <p>Your personal Web Application can be reached from any browser at: <a href="www.partner.netme.eu" style="color: #007BFF; text-decoration: none;">www.partner.netme.eu</a> </p>
                <p>If you have any questions or require further assistance, please do not hesitate to 
                reach out to our support team directly from the message section inside your profile.</p>
                <p>Once again, welcome to the NETME Partner community. We look forward to a 
                successful collaboration!</p>
                <p>Best regards,</p>
                <p>The NETME Team</p>
            </div>
        </div>`),
          await sendEmail(mailOptions);
        //@INFO SES HAVE SOME ISSUES CURRENTLY
        // const mailOptions = {
        //   from: '"NETME Partner" <' + process.env.EMAIL + ">",
        //   to: data.email,
        //   subject: "Welcome to Our Platform!",
        //   text: `Hello,\n
        //   Thank you for registering on our platform! We're excited to have you on board.\n
        //   Your requested is now accepted and now you can login with your register email and password of your account is ********.\n
        //   Thank you and Best regards\n
        //   Netme`,
        // };
        // await sendEmail(mailOptions);
      } else if (req.body.status == "Deleted") {
        // //console.log("partnerSubscriptionData", partnerSubscriptionData);
        // If rejected, cancel the Stripe subscription and refund payment
        if (!!partnerSubscriptionData) {
          const stripeRefund = await this.stripe.refunds.create({
            payment_intent: partnerSubscriptionData.stripePaymentId, // Payment intent ID from Stripe
          });
          // //console.log("stripeRefund", stripeRefund);
          // Cancel subscription in Stripe
          const cancelSubs = await this.stripe.subscriptions.cancel(
            partnerSubscriptionData.stripeSubscriptionId
          );
          // //console.log("cancelSubs", cancelSubs);
        }

        mailOptions.subject = "Update on Your NETME Partner Application";
        (mailOptions.text = `
            <div style="padding: 20px; background-color: white; border-radius: 0 0 8px 8px;">
                <p>Dear ${data.fullName},</p>
                <p>We hope this email finds you well.</p>
                <p>Thank you for your interest in becoming a NETME Partner. We greatly appreciate the time and effort you put into your application and your enthusiasm for collaborating with us.</p>
                <p>
                  After a thorough review of your application, we regret to inform you that we are unable to move 
                  forward with your request to become a NETME Partner at this time. Due to the high volume of 
                  applications we receive and the specific criteria we must adhere to, we are unfortunately not able to 
                  accept every application.
                </p>
                <p>Please understand that this decision is in no way a reflection of your capabilities or potential. We encourage you to reapply in the future, as our needs and criteria may evolve over time.</p>
                <p>If you have any questions or would like feedback on your application, please feel free to reach out 
                to our support <NAME_EMAIL>. We are here to assist you and provide any 
                information you may need.</p>
                <p>Thank you once again for your interest in NETME. We wish you all the best in your future 
                endeavors and hope to have the opportunity to work together at some point in the future.</p>
                <p>Best regards,</p>
                <p>The NETME Team</p>
            </div>
        </div>`),
          await sendEmail(mailOptions);
        await PartnerBusiness.findOneAndUpdate(
          { partnerId: req.body.partnerId },
          { deleted: true, isPartnerActive: false },
          { new: true }
        );
        await PartnerAds.deleteMany({ partnerId: req.body.partnerId });
      }

      if (data) {
        res.status(200).json({ data });
      } else throw Error("data not found");
    } catch (err) {
      let error = err.message;
      console.log(err);
      res.status(400).json({ error: error });
    }
  },
];

module.exports.getSinglePartner = [
  query("partnerId").not().isEmpty().withMessage("partnerId Field is required"),
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { partnerId } = req.query;

      const data = await Partner.aggregate([
        {
          $match: {
            _id: new mongoose.Types.ObjectId(partnerId),
          },
        },
        {
          $lookup: {
            from: "partnerbusinesses", // Collection name (usually lowercase and plural)
            localField: "_id",
            foreignField: "partnerId",
            as: "business",
          },
        },
        {
          $unwind: {
            path: "$business",
            preserveNullAndEmptyArrays: true, // Keep partners even if they have no business
          },
        },
        {
          $project: {
            // Include all fields from partner
            ...Object.fromEntries(
              Object.keys(Partner.schema.paths).map((key) => [key, 1])
            ),
            // Include business fields
            business: {
              $ifNull: ["$business", null],
            },
          },
        },
      ]);

      if (data && data.length > 0) {
        res.status(200).json({ data: data[0] });
      } else {
        throw new Error("Data not found");
      }
    } catch (err) {
      res.status(400).json({ error: err.message });
    }
  },
];

// module.exports.getSinglePartner = [
//   query("partnerId").not().isEmpty().withMessage("partnerId Field is required"),
//   async (req, res) => {
//     const errors = validationResult(req);
//     if (!errors.isEmpty()) {
//       return res.status(400).json({ errors: errors.array() });
//     }
//     try {
//       const { partnerId } = req.query;
//       const data = await Partner.findById({ _id: partnerId });
// //       console.log("partnerId", partnerId);
//       const findpartnerBusiness = await PartnerBusiness.findOne({
//         partnerId,
//       });
// //       console.log("data", data);
// //       console.log("findpartnerBusiness", findpartnerBusiness);
//       if (data) {
//         res
//           .status(200)
//           .json({ data: { ...data._doc, business: findpartnerBusiness._doc } });
//       } else throw Error("data not found");
//     } catch (err) {
//       let error = err.message;
//       res.status(400).json({ error: error });
//     }
//   },
// ];

module.exports.updateProfile = [
  body("businessId")
    .not()
    .isEmpty()
    .withMessage("businessId Field is required"),
  async (req, res) => {
    try {
      const updateBusiness = await PartnerBusiness.findByIdAndUpdate(
        { _id: req.body.businessId },
        { ...req.body },
        { new: true }
      );
      if (updateBusiness) {
        res.status(200).json({ data: updateBusiness });
      } else throw Error("Something went wrong");
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];
module.exports.deletePartner = [
  // params("partnerId").not().isEmpty().withMessage("partnerId Field is required"),
  async (req, res) => {
    try {
      // console.log("partnerId", req.params);
      const deletePartnerBusiness = await PartnerBusiness.findOneAndDelete({
        partnerId: req.params.id,
      });
      const deletePartner = await Partner.findByIdAndDelete(req.params.id);
      // const deletePartner = await Partner.findByIdAndDelete({
      //   _id: req.params.id,
      // });
      const deletePartnerSubscription =
        await PartnerSubscription.findOneAndDelete({
          partnerId: req.params.id,
        });

      // console.log("deletePartnerBusiness", deletePartnerBusiness);
      // console.log("deletePartner", deletePartner);
      // console.log("deletePartnerSubscription", deletePartnerSubscription);
      res.status(200).json({
        message: "Partner deleted successfully",
      });
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];

module.exports.getAllPatnerStamps = [
  async (req, res) => {
    try {
      const currentDate = moment();
      const lastMonthDate = moment().subtract(1, "months");

      const findStamp = await Stamp.find({ businessId: req.params.id }).sort({
        createdAt: -1,
      });
      if (findStamp) {
        const currentMonthData = await Stamp.find({
          businessId: req.params.id,
          createdAt: {
            $gte: currentDate.startOf("month").toDate(),
            $lte: currentDate.endOf("month").toDate(),
          },
        }).sort({ createdAt: -1 });
        const previousMonthData = await Stamp.find({
          businessId: req.params.id,
          createdAt: {
            $gte: lastMonthDate.startOf("month").toDate(),
            $lte: lastMonthDate.endOf("month").toDate(),
          },
        }).sort({ createdAt: -1 });
        const currentMonthCount = currentMonthData.length;
        const previousMonthCount = previousMonthData.length;
        res.status(200).json({
          data: findStamp,
          currentMonthDataCount: currentMonthCount,
          previousMonthDataCount: previousMonthCount,
          count: findStamp.length,
        });
      }
    } catch (err) {
      let error = err.message;
      res.status(400).json({ error });
    }
  },
];

module.exports.getMeetingDetails = async (req, res) => {
  try {
    let page = parseInt(req.query.page ? req.query.page : 1);
    let limit = parseInt(req.query.limit ? req.query.limit : 100);
    let skipValue = (page - 1) * limit;
    const { businessId } = req.query;

    const stats = await Invitations.aggregate([
      {
        $match: {
          businessId: new mongoose.Types.ObjectId(businessId),
        },
      },
      {
        $group: {
          _id: null,
          totalMeetings: {
            $sum: { $cond: [{ $eq: ["$status", "Accepted"] }, 1, 0] },
          },
          totalThisMonthMeetings: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ["$status", "Accepted"] },
                    {
                      $gt: [
                        "$date",
                        new Date(
                          moment().startOf("month").format("YYYY-MM-DD")
                        ),
                      ],
                    }, // $gt condition
                    {
                      $lt: [
                        "$date",
                        new Date(moment().endOf("month").format("YYYY-MM-DD")),
                      ],
                    }, // $lt condition
                  ],
                },
                1,
                0,
              ],
            },
          },
          totalPastMonthMeetings: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ["$status", "Accepted"] },
                    {
                      $gt: [
                        "$date",
                        new Date(
                          moment()
                            .subtract(1, "months")
                            .startOf("month")
                            .format("YYYY-MM-DD")
                        ),
                      ],
                    }, // $gt condition
                    {
                      $lt: [
                        "$date",
                        new Date(
                          moment()
                            .subtract(1, "months")
                            .endOf("month")
                            .format("YYYY-MM-DD")
                        ),
                      ],
                    },
                  ],
                },
                1,
                0,
              ],
            },
          },
        },
      },
    ]);

    const finalCount = stats[0];

    const totalMeetings = finalCount ? finalCount.totalMeetings : 0;
    const totalThisMonthMeetings = finalCount
      ? finalCount.totalThisMonthMeetings
      : 0;
    const totalPastMonthMeetings = finalCount
      ? finalCount.totalPastMonthMeetings
      : 0;

    res.status(200).json({
      totalMeetings,
      totalThisMonthMeetings,
      totalPastMonthMeetings,
    });
  } catch (err) {
    console.log(err);
    res.status(400).json({ error: "Something Went Wrong" });
  }
};
