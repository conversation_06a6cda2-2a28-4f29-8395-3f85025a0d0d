const {
  uploadFileToS3,
  getSignedUrl,
  imageUpload,
  uploadAnyFileToS3,
  profilePictureToS3,
} = require("../../util/uploadFile");
const { generateOTP } = require("../../util/generateOtp");
const OtpManager = require("../../models/Common/OtpManager");
const Partner = require("../../models/Partner/Partner");
const Admin = require("../../models/Admin/Admin");
const User = require("../../models/User/User");
const { sendEmail } = require("../../util/sendEmail");

module.exports.sendOtp = async (req, res) => {
  try {
    const { email, admin, user } = req.body;
    await OtpManager.deleteOne({
      email,
    });
    let sendReq = true;
    let otp = generateOTP();

    let findUser;

    if (admin) {
      findUser = await Admin.findOne({ email });
    } else if (user) {
      findUser = await User.findOne({ email });
    } else {
      findUser = await Partner.findOne({ email });
    }
    if (!findUser) {
      sendReq = false;
      res.status(400).json({
        message:
          "This email is not associated with an account. If you believe this is a mistake, please contact support.",
      });
    }

    if (sendReq) {
      let newOtp = await OtpManager.findOneAndUpdate(
        {
          email,
        },
        { email, otp },
        { new: true, upsert: true }
      );
      const name = findUser.fullName || findUser.name || findUser.userName;
      const lowerCaseStr = findUser.userType.toLowerCase();
      const userType =
        lowerCaseStr.charAt(0).toUpperCase() + lowerCaseStr.slice(1);
      if (newOtp) {
        const mailOptions = {
          from: `"NETME ${userType}" <` + process.env.EMAIL + ">", //"NETME Partner",
          to: email,
          subject: `Reset Your Password - NETME ${userType}`,
          text: `<div style="padding: 20px; background-color: white; border-radius: 0 0 8px 8px;">
                <p>Dear ${name},</p>
                <p>We received a request to reset your password for your NETME ${userType} account. To proceed with resetting your password, please use the One-Time Password (OTP) provided below:</p>
                <p><strong>Your OTP Code: ${otp}</strong></p>

                <p>This OTP is valid for the next 10 minutes. Please do not share this code with anyone. If you did not request a password reset, please ignore this email. Your account remains secure.</p>

                <p>For any further assistance, feel free to reach out to our support team.</p>

                <p>Thank you for being a valued NETME ${userType}.</p>

                <p>Best regards,</p>
                <p>The NETME ${userType} Team</p>
            </div>
        </div>`,
        };
        await sendEmail(mailOptions);
        res.status(200).json({ message: "Otp Generated Successfully" });
      } else throw Error("Something Went Wrong");
    }
  } catch (err) {
    let error = err.message;
    res.status(400).json({ error: error });
  }
};

module.exports.verifyOtp = async (req, res) => {
  try {
    const { email, otp } = req.body;

    let findOtp = await OtpManager.findOne({ email, otp }).sort({
      createdAt: -1,
    });
    if (findOtp) {
      await OtpManager.deleteOne({ email, otp });
      res.status(200).json({ message: "Verification Successfull" });
    } else throw Error("Not Valid Otp");
  } catch (err) {
    let error = err.message;
    res.status(400).json({ error: error });
  }
};

module.exports.getOtp = async (req, res) => {
  try {
    const { email } = req.query;

    let findOtp = await OtpManager.findOne({ email }).sort({
      createdAt: -1,
    });
    if (findOtp) {
      res.status(200).json({ message: "Verification Successfull", findOtp });
    } else throw Error("OTP not found");
  } catch (err) {
    let error = err.message;
    res.status(400).json({ error: error });
  }
};
module.exports.uploadFile = [
  async (req, res) => {
    try {
      if (req.files) {
        const imageFile = req.files.file;

        console.log("imageFile");
        const extension = imageFile.name.split(".").pop();
        const fileName = `Img${Math.random() * 9999999}.${extension}`;
        const thumbnailName = `thumbnail${
          Math.random() * 9999999
        }.${extension}`;
        let imgUrl = await uploadFileToS3(imageFile.data, fileName);
        let thumbnailUrl;
        if (!!req.body.thumbnail) {
          thumbnailUrl = await profilePictureToS3(
            imageFile.data,
            thumbnailName
          );
        }

        if (imgUrl) {
          imgUrl;
          res.status(200).json({ url: imgUrl, thumbnailUrl });
        }
      } else {
        res.status(400).json({ error: "File Not Found" });
      }
    } catch (err) {
      // console.log(err)
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];
module.exports.uploadAnyFile = [
  async (req, res) => {
    try {
      if (req.files) {
        const imageFile = req.files.file;
        console.log("imageFile");
        const extension = imageFile.name.split(".").pop();
        const fileName = `Img${Math.random() * 9999999}.${extension}`;
        let imgUrl = await uploadAnyFileToS3(imageFile.data, fileName);
        if (imgUrl) {
          imgUrl;
          res.status(200).json({ url: imgUrl });
        }
      } else {
        res.status(400).json({ error: "File Not Found" });
      }
    } catch (err) {
      // console.log(err)
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];
module.exports.uploadImage = [
  async (req, res) => {
    try {
      const partner = await Partner.findById({ _id: req.body.userId });
      if (!!partner.qrCode) {
        res.status(200).json({ url: partner.qrCode });
      } else {
        let imgUrl = await imageUpload(req.body.imageUrl, req.body.userId);

        await Partner.findByIdAndUpdate(
          { _id: req.body.userId },
          { qrCode: imgUrl }
        );
        if (imgUrl) {
          imgUrl;
          res.status(200).json({ url: imgUrl });
        }
      }
    } catch (err) {
      // console.log(err)
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];

module.exports.generateSignerUrl = [
  async (req, res) => {
    try {
      if (req.query.fileName) {
        let fileName = req.query.fileName;

        let imgUrl = await getSignedUrl(fileName);
        if (imgUrl) {
          res.status(200).json({ signerUrl: imgUrl });
        }
      } else {
        res.status(400).json({ error: "File Not Found" });
      }
    } catch (err) {
      // console.log(err)
      let error = err.message;
      res.status(400).json({ error: error });
    }
  },
];
// const check = async () => {
//   const final = await getSignedUrl("Img4974219.9329182785.png")
//   console.log(final)
// }
// check()
