const Subscription = require("../../models/Admin/subscription");
// const Package = require("../../models/Common/package");

exports.getPackages = async (req, res) => {
  try {
    const packages = await Subscription.find({ deleted: false });
    res.status(200).json({
      status: "success",
      data: packages,
    });
  } catch (error) {
    res.status(400).json({
      status: "failed",
      message: "package fetch error",
      error: error.message,
    });
  }
};
