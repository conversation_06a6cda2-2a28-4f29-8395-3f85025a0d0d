const express = require("express");
const router = express.Router();
const multer = require("multer");
const ChatController = require("../../controllers/Common/ChatController");
const { checkPermission } = require("../../middleware/checkPermission");

// Configure multer for audio file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    // Accept only audio files
    if (file.mimetype.startsWith("audio/")) {
      cb(null, true);
    } else {
      cb(new Error("Only audio files are allowed"), false);
    }
  },
});

// Initialize chat for a meeting
router.post(
  "/initialize/:invitationId",
  checkPermission(["USER"]),
  ChatController.initializeChat
);

// Get chat status
router.get(
  "/status/:chatId",
  checkPermission(["USER"]),
  ChatController.getChatStatus
);

// Get user's chats
router.get(
  "/user-chats",
  checkPermission(["USER"]),
  ChatController.getUserChats
);

// Check if a chat exists between two users
router.get(
  "/check-existing",
  checkPermission(["USER"]),
  ChatController.checkExistingChat
);

// Get chat messages
router.get(
  "/messages/:chatId",
  checkPermission(["USER"]),
  ChatController.getChatMessages
);

// Remove specific message
router.delete(
  "/messages/:chatId/:messageId",
  checkPermission(["USER"]),
  ChatController.removeMessage
);

// Send a message to a chat
router.post(
  "/messages/:chatId",
  checkPermission(["USER"]),
  ChatController.sendMessage
);

// Upload audio message
router.post(
  "/upload-audio",
  checkPermission(["USER"]),
  upload.single("audio"),
  ChatController.uploadAudioMessage
);

// Get chat data for re-invite
router.get(
  "/re-invite/:chatId",
  checkPermission(["USER"]),
  ChatController.getChatForReInvite
);

// Mark messages as read
router.post(
  "/mark-read/:chatId",
  checkPermission(["USER"]),
  ChatController.markMessagesAsRead
);

// Handle invitation acceptance
router.post(
  "/invitation-accepted/:invitationId",
  checkPermission(["USER"]),
  ChatController.handleInvitationAccepted
);

// Delete chat
router.delete("/:chatId", checkPermission(["USER"]), ChatController.deleteChat);

// Check pending invitations between users
router.get(
  "/pending-invitations/:userId",
  checkPermission(["USER"]),
  ChatController.checkPendingInvitations
);

// Check message limit
router.get(
  "/message-limit/:chatId",
  checkPermission(["USER"]),
  ChatController.checkMessageLimit
);

module.exports = router;
