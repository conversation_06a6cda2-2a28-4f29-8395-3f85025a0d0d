const { Router } = require("express");
const adsController = require("../../controllers/Partner/adsController");
const {
  CheckPartnerPermission,
} = require("../../middleware/checkPartnerPermission");
const { checkGuestAccess } = require("../../middleware/checkGuestAccess");

const router = Router();

router.post(
  "/createAds",
  CheckPartnerPermission(["PARTNER"]),
  adsController.createAds
);
router.delete(
  "/deleteAd/:id",
  CheckPartnerPermission(["PARTNER"]),
  adsController.deleteAd
);
router.post(
  "/createExternalAds",
  checkGuestAccess(),
  adsController.createExternalAds
);
router.put(
  "/updateAds",
  CheckPartnerPermission(["PARTNER"]),
  adsController.updateAds
);
router.put(
  "/updateAdsStatus",
  CheckPartnerPermission(["PARTNER"]),
  adsController.updateAdsStatus
);
router.get(
  "/getAds",
  CheckPartnerPermission(["PARTNER"]),
  adsController.getAds
);
router.get(
  "/getAdvtertisementStats",
  CheckPartnerPermission(["PARTNER"]),
  adsController.getAdvtertisementStats
);
router.get(
  "/getSingleAds",
  CheckPartnerPermission(["PARTNER"]),
  adsController.getSingleAds
);
router.get(
  "/getSingleRequestedAds/:id",
  CheckPartnerPermission(["PARTNER"]),
  adsController.getSingleRequestedAds
);
router.get(
  "/getAllAds",
  CheckPartnerPermission(["PARTNER"]),
  adsController.getAllAds
);
module.exports = router;
