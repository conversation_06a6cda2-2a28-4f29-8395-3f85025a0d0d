const { Router } = require("express");
const userController = require("../../controllers/User/userController");
const { checkPermission } = require("../../middleware/checkPermission");
const { optionalAuth } = require("../../middleware/optionalAuth");

const router = Router();

router.post(
  "/getUserUnderRadius",
  // optionalAuth,
  checkPermission(["USER"]),
  userController.getUsersUnderRadius
);

// Apply checkPermission middleware to all subsequent routes
router.use(checkPermission(["USER"]));

router.get("/getUser", userController.getUser);
router.post("/getSpecificUsers", userController.getSpecificUsers);
router.get("/getUserById", userController.getUserById);
router.put("/updateUser", userController.updateUser);
router.put("/updateBusinessRating", userController.updateBusinessRating);
router.put("/updatePassword", userController.updatePassword);
router.post("/createHelpCenter", userController.createHelpCenter);
router.get("/feedback/:feedbackId", userController.getFeedback);
router.put("/feedback/:feedbackId", userController.updateFeedback);
router.get("/feedback-status/:invitationId", userController.getFeedbackStatus);
router.post(
  "/group-feedback/:invitationId",
  userController.submitGroupFeedback
);
router.get(
  "/group-feedback/:invitationId",
  userController.getGroupFeedbackStatus
);
router.post("/report", userController.reportUser);
router.get("/getReportedUserById", userController.getReportedUserById);
router.post("/updateAnalytics", userController.updateAnalytics);

module.exports = router;
