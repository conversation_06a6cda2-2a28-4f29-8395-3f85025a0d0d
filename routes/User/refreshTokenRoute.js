const { Router } = require("express");
const refreshTokenController = require("../../controllers/User/refreshTokenController");

const router = Router();

/**
 * @route POST /api/refresh-token
 * @desc Refresh access token using refresh token
 * @access Public
 */
router.post("/refresh", refreshTokenController.refreshToken);

/**
 * @route POST /api/refresh-token/revoke
 * @desc Revoke a refresh token (logout)
 * @access Public
 */
router.post("/revoke", refreshTokenController.revokeRefreshToken);

module.exports = router;
