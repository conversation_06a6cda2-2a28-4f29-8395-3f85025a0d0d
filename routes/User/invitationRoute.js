const { Router } = require("express");
const invitationController = require("../../controllers/User/invitationController");
const { checkPermission } = require("../../middleware/checkPermission");
const { checkGuestAccess } = require("../../middleware/checkGuestAccess");
const router = Router();
router.get(
  "/getAllPlaces",
  checkPermission(["USER"]),
  invitationController.getAllPlaces
);
router.post(
  "/getBusinessUnderRadius",
  checkGuestAccess(),
  invitationController.getBusinessUnderRadius
);
router.get(
  "/getAllRequests",
  checkPermission(["USER"]),
  invitationController.getAllRequests
);
router.get(
  "/getMyInvitations",
  checkPermission(["USER"]),
  invitationController.getMyInvitations
);
router.get(
  "/getInvitations",
  checkPermission(["USER"]),
  invitationController.getInvitations
);
router.get(
  "/getInvitationById",
  checkPermission(["USER"]),
  invitationController.getInvitationById
);
router.get(
  "/getInvitationByLastAccepted",
  checkPermission(["USER"]),
  invitationController.getInvitationByLastAccepted
);
router.get(
  "/getInvitationsRequests",
  checkPermission(["USER"]),
  invitationController.getInvitationsRequests
);
router.post(
  "/inviteUser",
  checkPermission(["USER"]),
  invitationController.inviteUser
);
router.put(
  "/updateInvitation",
  checkPermission(["USER"]),
  invitationController.updateInvitation
);

// router.get(
//   "/checkExistingChat",
//   checkPermission(["USER"]),
//   invitationController.checkExistingChatBetweenUsers
// );

module.exports = router;
