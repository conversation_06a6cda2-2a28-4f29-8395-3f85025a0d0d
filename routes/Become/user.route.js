const express = require("express");
const {
  getUsers,
  createUser,
  uploadFile,
  sendEmail,
  sendGuestListEmail,
  saveNewsletter,
} = require("../../controllers/Become/user.controller");
const multer = require("multer");
const { checkToken } = require("../../util/checkToken");

const router = express.Router();
const upload = multer({ storage: multer.memoryStorage() });

router.route("/").get(getUsers).post(createUser);
router.route("/upload").post(upload.single("file"), uploadFile);
router.route("/email").post(checkToken, sendEmail);
router.route("/sendGuestListEmail").post(checkToken, sendGuestListEmail);
router.route("/saveNewsletter").post(checkToken, saveNewsletter);

module.exports = router;
