#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Set test environment
process.env.NODE_ENV = 'test';
process.env.TEST_MONGODB_URI = process.env.TEST_MONGODB_URI || 'mongodb://localhost:27017/netme_test';

console.log('Group Invitation System Test Runner');
console.log('==================================');
console.log(`Test Database: ${process.env.TEST_MONGODB_URI}`);
console.log('');

// Run the specific test file
const testFile = process.argv[2] || 'tests/group-invitation-system.test.js';
const jestArgs = [
  testFile,
  '--verbose',
  '--detectOpenHandles',
  '--forceExit',
  '--runInBand' // Run tests serially to avoid database conflicts
];

console.log(`Running tests: ${testFile}`);
console.log('');

const jest = spawn('npx', ['jest', ...jestArgs], {
  stdio: 'inherit',
  cwd: process.cwd()
});

jest.on('close', (code) => {
  console.log('');
  if (code === 0) {
    console.log('✅ All tests passed!');
  } else {
    console.log('❌ Some tests failed.');
  }
  process.exit(code);
});

jest.on('error', (error) => {
  console.error('Failed to start test runner:', error);
  process.exit(1);
});
