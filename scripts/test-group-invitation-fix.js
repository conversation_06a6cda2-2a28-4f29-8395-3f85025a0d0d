#!/usr/bin/env node

/**
 * Quick test script to verify the group invitation fix is working
 * This script simulates the scenario where multiple users accept invitations
 * and verifies they are properly added to chat participants
 */

const mongoose = require('mongoose');
const User = require('../models/User/User');
const Invitation = require('../models/User/invitations');
const Chat = require('../models/Common/Chat');

// Load environment variables
require('dotenv').config();

async function connectToDatabase() {
  try {
    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URI || 'mongodb://localhost:27017/netme';
    
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

async function testGroupInvitationFix() {
  console.log('🧪 Testing Group Invitation Fix');
  console.log('==============================');

  try {
    // Find a group invitation with multiple users
    const groupInvitation = await Invitation.findOne({
      isGroup: true,
      'users.1': { $exists: true }, // Has at least 2 users
      deleted: { $ne: true }
    }).populate('users.userId', 'userName email');

    if (!groupInvitation) {
      console.log('⚠️  No group invitations found. Creating a test scenario...');
      
      // Find some users to create a test invitation
      const users = await User.find({}).limit(3);
      if (users.length < 3) {
        console.log('❌ Need at least 3 users in database to test');
        return;
      }

      console.log('📝 Creating test group invitation...');
      const testInvitation = await Invitation.create({
        invitationBy: users[0]._id,
        users: [
          { userId: users[1]._id, status: 'Pending' },
          { userId: users[2]._id, status: 'Pending' }
        ],
        groupName: 'Test Group Fix',
        date: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        time: '14:00',
        isGroup: true
      });

      console.log(`✅ Created test invitation: ${testInvitation._id}`);
      return;
    }

    console.log(`📋 Found group invitation: ${groupInvitation._id}`);
    console.log(`👥 Group: ${groupInvitation.groupName || 'Unnamed Group'}`);
    console.log(`👤 Creator: ${groupInvitation.invitationBy}`);
    
    // Check current user statuses
    console.log('\n📊 Current User Statuses:');
    groupInvitation.users.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.userId.userName || user.userId.email} - ${user.status}`);
      if (user.acceptedAt) {
        console.log(`     Accepted at: ${user.acceptedAt}`);
      }
    });

    // Check if there's a chat for this invitation
    const chat = await Chat.findOne({ invitationId: groupInvitation._id });
    
    if (chat) {
      console.log('\n💬 Chat exists:');
      console.log(`   Chat ID: ${chat._id}`);
      console.log(`   Participants: ${chat.participants.length}`);
      
      console.log('\n👥 Chat Participants:');
      for (const participant of chat.participants) {
        const userId = participant.userId || participant;
        const user = await User.findById(userId).select('userName email');
        const joinedAt = participant.joinedAt ? ` (joined: ${participant.joinedAt})` : '';
        console.log(`   - ${user?.userName || user?.email || userId}${joinedAt}`);
      }

      // Check for accepted users not in chat participants
      const acceptedUsers = groupInvitation.users.filter(u => u.status === 'Accepted');
      const participantIds = chat.participants.map(p => (p.userId || p).toString());
      
      console.log('\n🔍 Verification:');
      console.log(`   Accepted users: ${acceptedUsers.length}`);
      console.log(`   Chat participants: ${chat.participants.length}`);
      
      const missingUsers = acceptedUsers.filter(u => 
        !participantIds.includes(u.userId._id.toString())
      );
      
      if (missingUsers.length > 0) {
        console.log('❌ ISSUE FOUND: Accepted users missing from chat participants:');
        missingUsers.forEach(user => {
          console.log(`   - ${user.userId.userName || user.userId.email}`);
        });
      } else {
        console.log('✅ All accepted users are in chat participants');
      }

      // Check if creator is in participants
      const creatorInParticipants = participantIds.includes(groupInvitation.invitationBy.toString());
      if (!creatorInParticipants) {
        console.log('❌ ISSUE: Creator is not in chat participants');
      } else {
        console.log('✅ Creator is in chat participants');
      }

    } else {
      console.log('\n💬 No chat exists for this invitation yet');
      
      const acceptedUsers = groupInvitation.users.filter(u => u.status === 'Accepted');
      if (acceptedUsers.length > 0) {
        console.log('⚠️  There are accepted users but no chat - this might indicate an issue');
      }
    }

    console.log('\n📈 Summary:');
    console.log(`   Total users invited: ${groupInvitation.users.length}`);
    console.log(`   Accepted: ${groupInvitation.users.filter(u => u.status === 'Accepted').length}`);
    console.log(`   Pending: ${groupInvitation.users.filter(u => u.status === 'Pending').length}`);
    console.log(`   Rejected: ${groupInvitation.users.filter(u => u.status === 'Rejected').length}`);
    
    if (chat) {
      console.log(`   Chat participants: ${chat.participants.length}`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

async function disconnectFromDatabase() {
  try {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  } catch (error) {
    console.error('❌ Error disconnecting from MongoDB:', error);
  }
}

async function runTest() {
  await connectToDatabase();
  
  try {
    await testGroupInvitationFix();
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  } finally {
    await disconnectFromDatabase();
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT. Gracefully shutting down...');
  await disconnectFromDatabase();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM. Gracefully shutting down...');
  await disconnectFromDatabase();
  process.exit(0);
});

// Run the test
runTest().catch((error) => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
