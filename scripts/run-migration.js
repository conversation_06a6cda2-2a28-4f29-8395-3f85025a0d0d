#!/usr/bin/env node

const mongoose = require("mongoose");
const {
  migrateGroupInvitationSystem,
  rollbackMigration,
} = require("../migrations/fix-group-invitation-system");

// Load environment variables
require("dotenv").config();

async function connectToDatabase() {
  try {
    const mongoUri =
      process.env.MONGODB_URI ||
      process.env.MONGO_URI ||
      "mongodb://localhost:27017/netmedb";

    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log("Connected to MongoDB");
  } catch (error) {
    console.error("Failed to connect to MongoDB:", error);
    process.exit(1);
  }
}

async function disconnectFromDatabase() {
  try {
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  } catch (error) {
    console.error("Error disconnecting from MongoDB:", error);
  }
}

async function runMigration() {
  const action = process.argv[2];

  console.log("Group Invitation System Migration Tool");
  console.log("=====================================");

  if (action === "rollback") {
    console.log("Running rollback migration...");
    console.log(
      "WARNING: This will revert the group invitation system changes!"
    );

    // Add a confirmation prompt in production
    if (process.env.NODE_ENV === "production") {
      console.log("Rollback is disabled in production for safety.");
      process.exit(1);
    }
  } else {
    console.log("Running forward migration...");
  }

  await connectToDatabase();

  try {
    if (action === "rollback") {
      await rollbackMigration();
    } else {
      await migrateGroupInvitationSystem();
    }

    console.log("Migration completed successfully!");
  } catch (error) {
    console.error("Migration failed:", error);
    process.exit(1);
  } finally {
    await disconnectFromDatabase();
  }
}

// Handle process termination
process.on("SIGINT", async () => {
  console.log("\nReceived SIGINT. Gracefully shutting down...");
  await disconnectFromDatabase();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("\nReceived SIGTERM. Gracefully shutting down...");
  await disconnectFromDatabase();
  process.exit(0);
});

// Run the migration
runMigration().catch((error) => {
  console.error("Unexpected error:", error);
  process.exit(1);
});
