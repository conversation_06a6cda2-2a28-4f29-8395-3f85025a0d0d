const cron = require("node-cron");
const Invitation = require("../models/User/invitations");
const User = require("../models/User/User");
const socketService = require("../services/socketService");
const {
  now,
  createUTCDateTime,
  parseTimeString,
} = require("../util/timeUtils");

// Helper to extract Date object for invitation's scheduled time in UTC
function getInvitationDateTime(invitation) {
  if (invitation.time) {
    // Use parseTimeString to handle different time formats
    const timeParts = parseTimeString(invitation.time);
    if (timeParts) {
      const timeStr = `${timeParts[0]
        .toString()
        .padStart(2, "0")}:${timeParts[1].toString().padStart(2, "0")}`;
      return createUTCDateTime(
        invitation.date.toISOString().split("T")[0],
        timeStr
      );
    }
  }
  // If no time specified, use the date as-is
  return new Date(invitation.date);
}

const invitationExpire = () => {
  // Cron job: runs every 10 minutes
  cron.schedule("*/10 * * * *", async () => {
    try {
      const currentTime = now(); // UTC current time
      // Find all pending invitations whose scheduled time has passed and are unseen
      const expiredInvitations = await Invitation.find({
        status: "Pending",
        deleted: { $ne: true },
        isSeen: false,
      }).populate("users.userId invitationBy");

      for (const invitation of expiredInvitations) {
        const scheduledDateTime = getInvitationDateTime(invitation);
        if (scheduledDateTime < currentTime) {
          // Mark as expired and deleted
          invitation.status = "Expired";
          invitation.deleted = true;
          invitation.expiredAt = currentTime; // UTC timestamp
          await invitation.save();

          // Prepare sender info
          const sender = invitation.invitationBy;
          // Find the first invitee who is not the sender
          const invitee = invitation.users
            .map((u) => u.userId)
            .find(
              (u) => u && sender && u._id.toString() !== sender._id.toString()
            );
          const inviteeName = invitee ? invitee.userName : "User";

          // Format date and time for message
          const formattedDate = scheduledDateTime.toLocaleDateString();
          const formattedTime = scheduledDateTime.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
          });

          // Compose warning message
          const warningMsg = `Your pending request with ${inviteeName} for ${formattedDate} at ${formattedTime} has expired.`;

          // Send real-time warning to sender via socket
          if (sender && sender._id) {
            socketService.emitToUser(
              sender._id.toString(),
              "invitationExpiredWarning",
              { message: warningMsg, invitationId: invitation._id }
            );
          }
          // Optionally, add a user notification or email here as well
        }
      }
    } catch (err) {
      console.error("[Invitation Expiry Cron] Error:", err);
    }
  });
};

module.exports = { invitationExpire }; // For consistency
