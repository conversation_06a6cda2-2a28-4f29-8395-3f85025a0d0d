const cron = require("node-cron");
const { sendCronPushNotification } = require("../util/sendPushNotification");
const Invitation = require("../models/User/invitations");
const { now, subtractTime, getTimeDifference } = require("../util/timeUtils");

// Schema for notification tracking
const mongoose = require("mongoose");
const notificationTrackingSchema = new mongoose.Schema({
  invitationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "invitation",
    required: true,
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  notificationType: {
    type: String,
    enum: ["24h_reminder", "3h_reminder"],
    required: true,
  },
  sentAt: {
    type: Date,
    default: () => new Date(), // Always UTC
  },
});

// Create a compound index to ensure uniqueness
notificationTrackingSchema.index(
  { invitationId: 1, userId: 1, notificationType: 1 },
  { unique: true }
);

// Create the model if it doesn't exist
let NotificationTracking;
try {
  NotificationTracking = mongoose.model("NotificationTracking");
} catch (error) {
  NotificationTracking = mongoose.model(
    "NotificationTracking",
    notificationTrackingSchema
  );
}

// Run a cron job every minute
const scheduleInviteNotificationCron = () => {
  cron.schedule("* * * * *", async () => {
    try {
      // Get current time in UTC
      const currentTime = now();
      console.log(`Running notification cron at ${currentTime.toISOString()}`);

      // Get all accepted invitations that are in the future
      const invitations = await Invitation.find({
        status: "Accepted",
        deleted: { $ne: true },
        date: { $gt: currentTime }, // Only future invitations (UTC comparison)
      }).populate({
        path: "users.userId invitationBy",
        select: "fcmToken isPremium userName timezone",
      });

      console.log(`Found ${invitations.length} upcoming accepted invitations`);

      for (const invitation of invitations) {
        // Get invitation time
        const invitationTime = new Date(invitation.date);

        // Calculate hours until meetup using UTC time
        const hoursUntil = getTimeDifference(
          invitationTime,
          currentTime,
          "hours"
        );
        console.log(
          `Invitation ${invitation._id}: ${hoursUntil.toFixed(
            2
          )} hours until meetup`
        );

        // Get authorized users based on invitation type
        let authorizedUsers = [];

        if (invitation.isGroup) {
          // For group invitations, only include users who have accepted + the creator
          authorizedUsers.push(invitation.invitationBy);

          invitation.users.forEach((userInvitation) => {
            if (userInvitation.status === "Accepted" && userInvitation.userId) {
              authorizedUsers.push(userInvitation.userId);
            }
          });
        } else {
          // For 1-on-1 invitations, include all users (creator + invitees)
          authorizedUsers = [
            invitation.invitationBy,
            ...invitation.users.map((u) => u.userId),
          ];
        }

        // Filter to only users with FCM tokens
        const usersWithTokens = authorizedUsers.filter(
          (user) => user && user.fcmToken
        );

        console.log(
          `Processing notifications for ${usersWithTokens.length} authorized users`
        );

        // Process each authorized user
        for (const user of usersWithTokens) {
          if (!user) continue;

          try {
            // Premium user 24h notification window (23.5h to 24.5h)
            if (user.isPremium && hoursUntil <= 24.5 && hoursUntil >= 23.5) {
              // Check if notification was already sent
              const existingNotification = await NotificationTracking.findOne({
                invitationId: invitation._id,
                userId: user._id,
                notificationType: "24h_reminder",
              });

              if (!existingNotification) {
                // Send notification
                await sendCronPushNotification({
                  title: "Upcoming Meetup",
                  body: "Your meetup is in 24 hours – the chat is now open for any last-minute coordination.",
                  fcmToken: user.fcmToken,
                  data: {
                    invitationId: invitation._id.toString(),
                    type: "24h_reminder",
                  },
                  userType: "USER",
                });

                // Record that notification was sent
                await NotificationTracking.create({
                  invitationId: invitation._id,
                  userId: user._id,
                  notificationType: "24h_reminder",
                  sentAt: currentTime, // UTC timestamp
                });

                console.log(
                  `Sent 24h reminder to ${user.userName} for invitation ${invitation._id}`
                );
              } else {
                console.log(
                  `24h reminder already sent to ${user.userName} for invitation ${invitation._id}`
                );
              }
            }

            // Standard user 3h notification window (2.5h to 3.5h)
            if (hoursUntil <= 3.5 && hoursUntil >= 2.5) {
              // Check if notification was already sent
              const existingNotification = await NotificationTracking.findOne({
                invitationId: invitation._id,
                userId: user._id,
                notificationType: "3h_reminder",
              });

              if (!existingNotification) {
                // Send notification
                await sendCronPushNotification({
                  title: "Upcoming Meetup",
                  body: "Your meetup is in 3 hours – the chat is now open for any last-minute coordination.",
                  fcmToken: user.fcmToken,
                  data: {
                    invitationId: invitation._id.toString(),
                    type: "3h_reminder",
                  },
                  userType: "USER",
                });

                // Record that notification was sent
                await NotificationTracking.create({
                  invitationId: invitation._id,
                  userId: user._id,
                  notificationType: "3h_reminder",
                  sentAt: currentTime, // UTC timestamp
                });

                console.log(
                  `Sent 3h reminder to ${user.userName} for invitation ${invitation._id}`
                );
              } else {
                console.log(
                  `3h reminder already sent to ${user.userName} for invitation ${invitation._id}`
                );
              }
            }
          } catch (error) {
            console.error(
              `Error processing notifications for ${
                user?.userName || "unknown user"
              }:`,
              error
            );
          }
        }
      }

      // Clean up old notification tracking records (older than 7 days)
      const oneWeekAgo = subtractTime(currentTime, 7, "days");
      await NotificationTracking.deleteMany({ sentAt: { $lt: oneWeekAgo } });
    } catch (error) {
      console.error("Error in notification cron job:", error);
    }
  });
};

module.exports = { scheduleInviteNotificationCron };
