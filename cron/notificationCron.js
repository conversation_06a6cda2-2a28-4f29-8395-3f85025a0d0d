const cron = require("node-cron");
const notification = require("../models/Common/notification");
const { sendCronPushNotification } = require("../util/sendPushNotification");
const Partner = require("../models/Partner/Partner");
const User = require("../models/User/User");
const Business = require("../models/Partner/partnerBusiness");
const { now, formatForAPI } = require("../util/timeUtils");

// Run a cron job every minute
const scheduleNotificationCron = () => {
  cron.schedule("* * * * *", async () => {
    try {
      const currentTime = now();
      const currentHour = currentTime.getUTCHours(); // UTC time in hours
      const currentMinute = currentTime.getUTCMinutes(); // UTC time in minutes
      const currentDate = formatForAPI(currentTime).split("T")[0]; // Get current date in YYYY-MM-DD (UTC)
      const formattedMinute = currentMinute.toString().padStart(2, "0");

      // Format hour and minute
      const formattedTime = `${currentHour}:${formattedMinute}`;
      console.log("`${currentHour}:${currentMinute}`", `${formattedTime}`);
      // Find notifications that are scheduled for the current date and time (UTC)
      const notifications = await notification.find({
        releaseDate: new Date(currentDate), // Match by release date (UTC)
        releaseTime: formattedTime, // Match by release time in UTC
        status: { $in: ["Upcoming", "Accepted"] }, // Only send notifications marked as "Upcoming"
      });
      // Send notifications
      // console.log("notifications", notifications);
      notifications.forEach(async (notification) => {
        let fcmTokens = []; // Store FCM tokens

        if (notification.userType === "PARTNER") {
          console.log("Partner called in Cron");
          // Fetch partner and check if their business city matches with notification cities
          const partner = await Partner.findById({
            _id: notification.partnerId,
          });
          const partnerBusiness = await Business.findOne({
            partnerId: notification.partnerId,
          });
          const users = await User.find({});
          // const partnerBusiness = await Business.findOne(partner.partnerId); // Assuming partnerBusiness reference exists

          if (
            partner &&
            (notification.cities.includes(partnerBusiness.city) ||
              notification.cities.includes("All"))
          ) {
            users.forEach((user) => {
              if (
                user.fcmToken &&
                (notification.cities.includes(user.city) ||
                  notification.cities.includes("All"))
              ) {
                fcmTokens.push(user.fcmToken); // Add user's FCM token to the list if city matches
              }
            });
            //fcmTokens.push(partner.fcmToken); // Add partner FCM token to the list if city matches
          }
        } else {
          console.log("User cron");
          // Fetch all users and check if their city matches with notification cities
          const users = await User.find({});
          users.forEach((user) => {
            if (
              user.fcmToken &&
              (notification.cities.includes(user.city) ||
                notification.cities.includes("All"))
            ) {
              fcmTokens.push(user.fcmToken); // Add user's FCM token to the list if city matches
            }
          });
        }
        // Send notifications for all tokens (use Promise.all to handle multiple FCM tokens)
        for (const fcmToken of fcmTokens) {
          // console.log("fcmToken", fcmToken);
          try {
            await sendCronPushNotification({
              title: notification.title,
              body: notification.body,
              fcmToken: fcmToken, // Use the current token in the loop
              data: {},
              userType: "USER", //notification.userType,
            });
          } catch (error) {
            console.error("Error sending notification:", error);
          }
        }

        // After sending, update the notification status
        notification.status = "Released";
        await notification.save();
      });
    } catch (error) {
      console.error("Error sending scheduled notifications:", error);
    }
  });
};
module.exports = { scheduleNotificationCron };
