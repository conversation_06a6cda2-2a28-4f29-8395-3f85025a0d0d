const cron = require("node-cron");
const User = require("../models/User/User");

// Run a cron job every minute
const scheduleUserCron = () => {
  cron.schedule("0 0 * * *", async () => {
    try {
      const users = await User.find({});
      for (const user of users) {
        await User.updateOne(
          { _id: user._id },
          { $set: { dailyInviteCount: 0 } }
        );
      }
    } catch (error) {
      console.error("Error sending scheduled user cron:", error);
    }
  });
};
module.exports = { scheduleUserCron };
