# NetMe Chat System Documentation

This directory contains comprehensive documentation for the NetMe Chat System, which uses Socket.IO for real-time communication.

## Documentation Files

- **[socket_io_flutter_implementation.md](./socket_io_flutter_implementation.md)**: Complete guide for Flutter developers to implement the Socket.IO client for the NetMe chat system.
- **[chat_api_reference.md](./chat_api_reference.md)**: API reference for the chat system endpoints.

## Features Implemented

The NetMe Chat System includes the following features:

1. **Real-time messaging** using Socket.IO
2. **Temporary chats** with specific activation windows
3. **Message limitations** for standard users (2 messages per chat)
4. **Invitation handling** with automatic info messages
5. **Chat reuse** for the same users
6. **Audio messages** support
7. **Group chat** capabilities
8. **Push notifications** for offline users
9. **Typing indicators** and read receipts
10. **Chat deletion** functionality

## Implementation Overview

The chat system consists of:

- **Backend**: Node.js server with Express and Socket.IO
- **Database**: MongoDB for chat data and Firebase for real-time messaging
- **Client**: Flutter implementation for mobile apps

## Getting Started

For Flutter developers integrating with the NetMe chat system:

1. Start with the [Socket.IO Flutter Implementation Guide](./socket_io_flutter_implementation.md)
2. Review the [Chat API Reference](./chat_api_reference.md) for available endpoints
3. Set up the required dependencies in your Flutter project
4. Implement the Socket.IO client following the provided examples

## Support

For questions or issues regarding the chat system implementation, please contact the NetMe development team.
