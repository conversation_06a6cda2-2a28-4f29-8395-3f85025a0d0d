# 1-to-1 Meeting Feedback API Guide

## Overview

This guide provides comprehensive documentation for the updated 1-to-1 meeting feedback system. The system now allows **both users** in a 1-to-1 meeting to provide feedback about each other's attendance, making it more fair and production-ready.

## Key Changes

### ✅ What's New:

- **Dual Feedback Records**: Both users get their own feedback record to submit
- **Proper Validation**: Users can only update their own feedback
- **Duplicate Prevention**: Users cannot submit feedback twice
- **Enhanced Security**: Proper authorization checks
- **Better Error Handling**: Clear error messages and validation

### 🔄 How It Works:

1. When a 1-to-1 invitation is created, **two feedback records** are generated:
   - One for the invitation creator (about the invitee's attendance)
   - One for the invitee (about the creator's attendance)
2. Each user can only update their own feedback record
3. Both users can provide "yes" (attended) or "no" (didn't attend) feedback
4. Strikes are applied when someone reports the other person as "no-show"

## API Endpoints

### 1. Update Feedback (Enhanced)

**Endpoint:** `PUT /api/user/feedback/:feedbackId`

**Description:** Submit feedback about the other user's attendance in a 1-to-1 meeting.

**Headers:**

```
Authorization: Bearer <your_jwt_token>
Content-Type: application/json
```

**Path Parameters:**

- `feedbackId` (string, required): The ID of the feedback record to update

**Request Body:**

```json
{
  "status": "yes"
}
```

**Request Body Fields:**

- `status` (string, required): Either "yes" (other user attended) or "no" (other user didn't attend)

**Success Response (200):**

```json
{
  "feedback": {
    "_id": "feedback_id",
    "userId": "current_user_id",
    "feedBackFor": "other_user_id",
    "given": true,
    "status": "yes",
    "submittedAt": "2024-01-15T10:30:00.000Z",
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  },
  "message": "Feedback submitted successfully"
}
```

**Error Responses:**

400 - Validation Error:

```json
{
  "errors": [
    {
      "msg": "Status must be either 'yes' or 'no'",
      "param": "status",
      "location": "body"
    }
  ]
}
```

400 - Already Submitted:

```json
{
  "error": "Feedback has already been submitted for this meeting"
}
```

403 - Unauthorized:

```json
{
  "error": "You are not authorized to update this feedback"
}
```

404 - Not Found:

```json
{
  "error": "Feedback record not found"
}
```

### 2. Get Feedback Status (New)

**Endpoint:** `GET /api/user/feedback-status/:invitationId`

**Description:** Get the feedback status for a 1-to-1 meeting, showing both users' feedback submission status.

**Headers:**

```
Authorization: Bearer <your_jwt_token>
```

**Path Parameters:**

- `invitationId` (string, required): The ID of the 1-to-1 meeting invitation

**Success Response (200):**

```json
{
  "invitationDetails": {
    "_id": "invitation_id",
    "date": "2024-01-15T10:00:00.000Z",
    "time": "10:00",
    "isGroup": false,
    "status": "Accepted"
  },
  "currentUser": {
    "_id": "current_user_id",
    "feedback": {
      "_id": "feedback_id_1",
      "given": true,
      "status": "yes",
      "submittedAt": "2024-01-15T10:30:00.000Z",
      "feedBackFor": "other_user_id"
    },
    "hasSubmittedFeedback": true
  },
  "otherUser": {
    "_id": "other_user_id",
    "userName": "John Doe",
    "feedback": {
      "_id": "feedback_id_2",
      "given": false,
      "status": "no",
      "submittedAt": null
    },
    "hasSubmittedFeedback": false
  },
  "bothUsersSubmitted": false
}
```

**Response Fields:**

- `invitationDetails`: Basic meeting information
- `currentUser`: Current user's feedback information
- `otherUser`: Other user's feedback information (limited details)
- `bothUsersSubmitted`: Whether both users have submitted their feedback

**Error Responses:**

400 - Wrong Meeting Type:

```json
{
  "error": "This endpoint is only for 1-to-1 meetings. Use group-feedback endpoint for group meetings."
}
```

403 - Unauthorized:

```json
{
  "error": "You are not authorized to view feedback for this meeting"
}
```

404 - Not Found:

```json
{
  "error": "Invitation not found"
}
```

## Postman Collection

### Environment Variables

```
base_url: http://localhost:3000/api
auth_token: <your_jwt_token>
invitation_id: <test_1to1_invitation_id>
feedback_id: <test_feedback_id>
```

### Collection Structure

#### 1. Get Feedback Status

```
Method: GET
URL: {{base_url}}/user/feedback-status/{{invitation_id}}
Headers:
  Authorization: Bearer {{auth_token}}
```

#### 2. Submit Feedback - User Attended

```
Method: PUT
URL: {{base_url}}/user/feedback/{{feedback_id}}
Headers:
  Authorization: Bearer {{auth_token}}
  Content-Type: application/json
Body (raw JSON):
{
  "status": "yes"
}
```

#### 3. Submit Feedback - User Didn't Attend

```
Method: PUT
URL: {{base_url}}/user/feedback/{{feedback_id}}
Headers:
  Authorization: Bearer {{auth_token}}
  Content-Type: application/json
Body (raw JSON):
{
  "status": "no"
}
```

### Test Scripts

Add this to the "Tests" tab of your requests:

```javascript
// For Get Feedback Status
pm.test("Status code is 200", function () {
  pm.response.to.have.status(200);
});

pm.test("Response has required fields", function () {
  const jsonData = pm.response.json();
  pm.expect(jsonData).to.have.property("invitationDetails");
  pm.expect(jsonData).to.have.property("currentUser");
  pm.expect(jsonData).to.have.property("otherUser");
  pm.expect(jsonData).to.have.property("bothUsersSubmitted");
});

// For Update Feedback
pm.test("Status code is 200", function () {
  pm.response.to.have.status(200);
});

pm.test("Response has feedback object", function () {
  const jsonData = pm.response.json();
  pm.expect(jsonData).to.have.property("feedback");
  pm.expect(jsonData).to.have.property("message");
  pm.expect(jsonData.feedback).to.have.property("given", true);
});
```

## Flutter Implementation Guide

### 1. Data Models

```dart
// models/feedback_status.dart
class FeedbackStatus {
  final InvitationDetails invitationDetails;
  final UserFeedback currentUser;
  final UserFeedback otherUser;
  final bool bothUsersSubmitted;

  FeedbackStatus({
    required this.invitationDetails,
    required this.currentUser,
    required this.otherUser,
    required this.bothUsersSubmitted,
  });

  factory FeedbackStatus.fromJson(Map<String, dynamic> json) {
    return FeedbackStatus(
      invitationDetails: InvitationDetails.fromJson(json['invitationDetails']),
      currentUser: UserFeedback.fromJson(json['currentUser']),
      otherUser: UserFeedback.fromJson(json['otherUser']),
      bothUsersSubmitted: json['bothUsersSubmitted'],
    );
  }
}

class UserFeedback {
  final String id;
  final String? userName;
  final FeedbackRecord? feedback;
  final bool hasSubmittedFeedback;

  UserFeedback({
    required this.id,
    this.userName,
    this.feedback,
    required this.hasSubmittedFeedback,
  });

  factory UserFeedback.fromJson(Map<String, dynamic> json) {
    return UserFeedback(
      id: json['_id'],
      userName: json['userName'],
      feedback: json['feedback'] != null
          ? FeedbackRecord.fromJson(json['feedback'])
          : null,
      hasSubmittedFeedback: json['hasSubmittedFeedback'],
    );
  }
}

class FeedbackRecord {
  final String id;
  final bool given;
  final String status;
  final DateTime? submittedAt;
  final String? feedBackFor;

  FeedbackRecord({
    required this.id,
    required this.given,
    required this.status,
    this.submittedAt,
    this.feedBackFor,
  });

  factory FeedbackRecord.fromJson(Map<String, dynamic> json) {
    return FeedbackRecord(
      id: json['_id'],
      given: json['given'],
      status: json['status'],
      submittedAt: json['submittedAt'] != null
          ? DateTime.parse(json['submittedAt'])
          : null,
      feedBackFor: json['feedBackFor'],
    );
  }
}

class InvitationDetails {
  final String id;
  final DateTime date;
  final String time;
  final bool isGroup;
  final String status;

  InvitationDetails({
    required this.id,
    required this.date,
    required this.time,
    required this.isGroup,
    required this.status,
  });

  factory InvitationDetails.fromJson(Map<String, dynamic> json) {
    return InvitationDetails(
      id: json['_id'],
      date: DateTime.parse(json['date']),
      time: json['time'],
      isGroup: json['isGroup'],
      status: json['status'],
    );
  }
}
```

### 2. API Service

```dart
// services/feedback_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;

class FeedbackService {
  final String baseUrl;
  final String authToken;

  FeedbackService({
    required this.baseUrl,
    required this.authToken,
  });

  Map<String, String> get _headers => {
    'Authorization': 'Bearer $authToken',
    'Content-Type': 'application/json',
  };

  Future<FeedbackStatus> getFeedbackStatus(String invitationId) async {
    final url = Uri.parse('$baseUrl/user/feedback-status/$invitationId');

    final response = await http.get(url, headers: _headers);

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return FeedbackStatus.fromJson(data);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get feedback status');
    }
  }

  Future<FeedbackRecord> updateFeedback({
    required String feedbackId,
    required String status, // "yes" or "no"
  }) async {
    final url = Uri.parse('$baseUrl/user/feedback/$feedbackId');

    final body = {
      'status': status,
    };

    final response = await http.put(
      url,
      headers: _headers,
      body: jsonEncode(body),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return FeedbackRecord.fromJson(data['feedback']);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to update feedback');
    }
  }
}
```

## Business Logic Summary

### Key Features

1. **Dual Feedback System**: Both users can provide feedback about each other
2. **Ownership Validation**: Users can only update their own feedback records
3. **Duplicate Prevention**: Users cannot submit feedback twice
4. **Strike Application**: Strikes are applied when someone reports a no-show
5. **Real-time Status**: Check feedback status for both users

### Security Features

- **Authorization**: Only meeting participants can access feedback
- **Ownership**: Users can only modify their own feedback
- **Validation**: Proper input validation and error handling
- **Audit Trail**: Timestamps for all feedback submissions

### Error Handling

- Clear error messages for different scenarios
- Graceful handling of network errors
- User-friendly feedback for all operations
- Proper HTTP status codes

This implementation provides a complete, production-ready 1-to-1 feedback system where both users can fairly provide feedback about each other's attendance.
