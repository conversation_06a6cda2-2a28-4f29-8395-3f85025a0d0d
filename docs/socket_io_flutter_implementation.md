# NetMe Chat Implementation Guide for Flutter

This document provides comprehensive instructions for implementing the Socket.IO-based chat system in the NetMe Flutter mobile application.

## Table of Contents

1. [Overview](#overview)
2. [Requirements](#requirements)
3. [Installation](#installation)
4. [Socket.IO Client Implementation](#socketio-client-implementation)
5. [Chat Features Implementation](#chat-features-implementation)
6. [Premium vs Standard User Features](#premium-vs-standard-user-features)
7. [Group Chat Implementation](#group-chat-implementation)
8. [Push Notifications](#push-notifications)
9. [Troubleshooting](#troubleshooting)
10. [API Reference](#api-reference)

## Overview

The NetMe chat system is designed to provide temporary chat functionality for confirmed invitations, with different activation windows for standard and premium users. The system uses Socket.IO for real-time communication and integrates with the existing invitation system.

### Key Features

- **Temporary Chat Windows**: Chats are only active during specific time windows relative to meeting times
- **Different Access Levels**: Premium and standard users have different chat activation times
- **Real-time Messaging**: Instant message delivery and typing indicators
- **Read Receipts**: Track which users have read messages
- **Group Chat Support**: For group invitations with special joining behavior
- **Message Limitations**: Standard users are limited to 2 messages per chat
- **Info Messages**: Automatic system messages for invitation acceptance
- **Push Notifications**: For offline users

## Requirements

- Flutter SDK 2.10.0 or higher
- Dart 2.16.0 or higher
- socket_io_client: ^2.0.0
- flutter_local_notifications: ^9.5.0 (for push notifications)
- Provider or Bloc for state management

## Installation

Add the following dependencies to your `pubspec.yaml` file:

```yaml
dependencies:
  socket_io_client: ^2.0.0
  flutter_secure_storage: ^5.0.2
  flutter_local_notifications: ^9.5.0
  intl: ^0.17.0
  provider: ^6.0.3
```

Run `flutter pub get` to install the dependencies.

## Socket.IO Client Implementation

### 1. Create a Socket.IO Service

Create a new file `lib/services/socket_service.dart`:

```dart
import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SocketService {
  // Singleton pattern
  static final SocketService _instance = SocketService._internal();
  factory SocketService() => _instance;
  SocketService._internal();

  // Socket instance
  IO.Socket? _socket;
  final _storage = FlutterSecureStorage();

  // Stream controllers for different events
  final _connectedController = StreamController<bool>.broadcast();
  final _messageController = StreamController<Map<String, dynamic>>.broadcast();
  final _typingController = StreamController<Map<String, dynamic>>.broadcast();
  final _readController = StreamController<Map<String, dynamic>>.broadcast();
  final _errorController = StreamController<Map<String, dynamic>>.broadcast();
  final _statusController = StreamController<Map<String, dynamic>>.broadcast();

  // Streams
  Stream<bool> get connected => _connectedController.stream;
  Stream<Map<String, dynamic>> get messages => _messageController.stream;
  Stream<Map<String, dynamic>> get typing => _typingController.stream;
  Stream<Map<String, dynamic>> get read => _readController.stream;
  Stream<Map<String, dynamic>> get errors => _errorController.stream;
  Stream<Map<String, dynamic>> get chatStatus => _statusController.stream;

  // Initialize socket connection
  Future<void> initSocket(String serverUrl) async {
    // Get auth token
    final token = await _storage.read(key: 'auth_token');

    if (token == null) {
      _errorController.add({
        'message': 'Authentication token not found'
      });
      return;
    }

    // Disconnect existing socket if any
    disconnect();

    try {
      // Create socket instance
      _socket = IO.io(serverUrl, <String, dynamic>{
        'transports': ['websocket'],
        'autoConnect': false,
        'auth': {
          'token': token
        }
      });

      // Connect to server
      _socket!.connect();

      // Setup event listeners
      _setupSocketListeners();
    } catch (e) {
      _errorController.add({
        'message': 'Failed to connect: $e'
      });
    }
  }

  // Setup socket event listeners
  void _setupSocketListeners() {
    _socket!.on('connect', (_) {
      _connectedController.add(true);
      if (kDebugMode) {
        print('Socket connected');
      }
    });

    _socket!.on('disconnect', (_) {
      _connectedController.add(false);
      if (kDebugMode) {
        print('Socket disconnected');
      }
    });

    _socket!.on('new_message', (data) {
      _messageController.add(data);
    });

    _socket!.on('typing_indicator', (data) {
      _typingController.add(data);
    });

    _socket!.on('messages_read', (data) {
      _readController.add(data);
    });

    _socket!.on('chat_status', (data) {
      _statusController.add(data);
    });

    _socket!.on('error', (data) {
      _errorController.add(data);
    });

    _socket!.onError((error) {
      _errorController.add({
        'message': 'Socket error: $error'
      });
    });
  }

  // Join a chat room
  void joinChat(String chatId) {
    if (_socket != null && _socket!.connected) {
      _socket!.emit('join_chat', {'chatId': chatId});
    } else {
      _errorController.add({
        'message': 'Socket not connected'
      });
    }
  }

  // Send a message
  void sendMessage({
    required String chatId,
    required String content,
    bool isAudio = false,
    String? audioUrl,
    bool isInfo = false
  }) {
    if (_socket != null && _socket!.connected) {
      _socket!.emit('send_message', {
        'chatId': chatId,
        'content': content,
        'isAudio': isAudio,
        'audioUrl': audioUrl,
        'isInfo': isInfo
      });
    } else {
      _errorController.add({
        'message': 'Socket not connected'
      });
    }
  }

  // Send typing indicator
  void sendTyping(String chatId, bool isTyping) {
    if (_socket != null && _socket!.connected) {
      _socket!.emit('typing', {
        'chatId': chatId,
        'isTyping': isTyping
      });
    }
  }

  // Mark messages as read
  void markRead(String chatId) {
    if (_socket != null && _socket!.connected) {
      _socket!.emit('mark_read', {
        'chatId': chatId
      });
    }
  }

  // Disconnect socket
  void disconnect() {
    if (_socket != null) {
      _socket!.disconnect();
      _socket!.dispose();
      _socket = null;
      _connectedController.add(false);
    }
  }

  // Dispose resources
  void dispose() {
    disconnect();
    _connectedController.close();
    _messageController.close();
    _typingController.close();
    _readController.close();
    _errorController.close();
    _statusController.close();
  }
}
```

### 2. Initialize the Socket Service

In your app initialization (e.g., `main.dart` or a startup service):

```dart
import 'package:flutter/material.dart';
import 'services/socket_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize socket service
  final socketService = SocketService();
  await socketService.initSocket('https://your-api-server.com'); // Replace with your server URL

  runApp(MyApp());
}
```

## Chat Features Implementation

### 1. Create a Chat Model

Create a file `lib/models/chat_message.dart`:

```dart
class ChatMessage {
  final String id;
  final String chatId;
  final String content;
  final bool isAudio;
  final String? audioUrl;
  final bool isInfo;
  final Sender sender;
  final DateTime timestamp;
  final Map<String, bool> read;

  ChatMessage({
    required this.id,
    required this.chatId,
    required this.content,
    required this.isAudio,
    this.audioUrl,
    required this.isInfo,
    required this.sender,
    required this.timestamp,
    required this.read,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['_id'],
      chatId: json['chatId'],
      content: json['content'],
      isAudio: json['isAudio'] ?? false,
      audioUrl: json['audioUrl'],
      isInfo: json['isInfo'] ?? false,
      sender: Sender.fromJson(json['sender']),
      timestamp: DateTime.parse(json['timestamp']),
      read: Map<String, bool>.from(json['read'] ?? {}),
    );
  }
}

class Sender {
  final String id;
  final String name;

  Sender({required this.id, required this.name});

  factory Sender.fromJson(Map<String, dynamic> json) {
    return Sender(
      id: json['_id'],
      name: json['name'],
    );
  }
}

class ChatStatus {
  final String status;
  final String statusColor;
  final String statusMessage;
  final bool canSendMessage;
  final int? timeUntilOpen;
  final DateTime? chatOpenTime;
  final DateTime? chatCloseTime;

  ChatStatus({
    required this.status,
    required this.statusColor,
    required this.statusMessage,
    required this.canSendMessage,
    this.timeUntilOpen,
    this.chatOpenTime,
    this.chatCloseTime,
  });

  factory ChatStatus.fromJson(Map<String, dynamic> json) {
    return ChatStatus(
      status: json['status'],
      statusColor: json['statusColor'],
      statusMessage: json['statusMessage'],
      canSendMessage: json['canSendMessage'] ?? false,
      timeUntilOpen: json['timeUntilOpen'],
      chatOpenTime: json['chatOpenTime'] != null
          ? DateTime.parse(json['chatOpenTime'])
          : null,
      chatCloseTime: json['chatCloseTime'] != null
          ? DateTime.parse(json['chatCloseTime'])
          : null,
    );
  }
}
```

### 2. Create a Chat Provider

Create a file `lib/providers/chat_provider.dart`:

```dart
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/chat_message.dart';
import '../services/socket_service.dart';

class ChatProvider with ChangeNotifier {
  final SocketService _socketService = SocketService();
  final List<ChatMessage> _messages = [];
  ChatStatus? _chatStatus;
  String? _currentChatId;
  Map<String, bool> _typingUsers = {};
  bool _isConnected = false;

  // Getters
  List<ChatMessage> get messages => _messages;
  ChatStatus? get chatStatus => _chatStatus;
  bool get isConnected => _isConnected;
  Map<String, bool> get typingUsers => _typingUsers;

  // Constructor - initialize listeners
  ChatProvider() {
    _initListeners();
  }

  // Initialize socket listeners
  void _initListeners() {
    _socketService.connected.listen((connected) {
      _isConnected = connected;
      notifyListeners();
    });

    _socketService.messages.listen((data) {
      final message = ChatMessage.fromJson(data);
      if (_currentChatId == message.chatId) {
        _addMessage(message);
        // Automatically mark messages as read when received
        _socketService.markRead(message.chatId);
      }
    });

    _socketService.typing.listen((data) {
      if (_currentChatId == data['chatId']) {
        final userId = data['userId'];
        final isTyping = data['isTyping'];
        _typingUsers = {..._typingUsers, userId: isTyping};
        notifyListeners();
      }
    });

    _socketService.chatStatus.listen((data) {
      if (_currentChatId == data['chatId']) {
        _chatStatus = ChatStatus.fromJson(data);
        notifyListeners();
      }
    });

    _socketService.errors.listen((data) {
      // Handle errors appropriately
      if (kDebugMode) {
        print('Socket error: ${data['message']}');
      }
    });
  }

  // Join a chat room
  void joinChat(String chatId) {
    _currentChatId = chatId;
    _messages.clear();
    _typingUsers = {};
    _socketService.joinChat(chatId);
    notifyListeners();
  }

  // Send a message
  void sendMessage({
    required String content,
    bool isAudio = false,
    String? audioUrl,
    bool isInfo = false,
  }) {
    if (_currentChatId == null) return;

    _socketService.sendMessage(
      chatId: _currentChatId!,
      content: content,
      isAudio: isAudio,
      audioUrl: audioUrl,
      isInfo: isInfo,
    );
  }

  // Send typing indicator
  void sendTyping(bool isTyping) {
    if (_currentChatId == null) return;
    _socketService.sendTyping(_currentChatId!, isTyping);
  }

  // Mark messages as read
  void markMessagesAsRead() {
    if (_currentChatId == null) return;
    _socketService.markRead(_currentChatId!);
  }

  // Add a message to the list
  void _addMessage(ChatMessage message) {
    // Check if message already exists
    final index = _messages.indexWhere((m) => m.id == message.id);
    if (index >= 0) {
      _messages[index] = message;
    } else {
      _messages.add(message);
      // Sort messages by timestamp
      _messages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    }
    notifyListeners();
  }

  // Leave current chat
  void leaveChat() {
    _currentChatId = null;
    _messages.clear();
    _typingUsers = {};
    _chatStatus = null;
    notifyListeners();
  }

  // Dispose
  @override
  void dispose() {
    // No need to dispose socket service as it's a singleton
    super.dispose();
  }
}
```

### 3. Create a Chat UI

Create a file `lib/screens/chat_screen.dart`:

```dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/chat_provider.dart';
import '../models/chat_message.dart';

class ChatScreen extends StatefulWidget {
  final String chatId;
  final String chatTitle;
  final DateTime meetingTime;
  final bool isGroupChat;

  const ChatScreen({
    Key? key,
    required this.chatId,
    required this.chatTitle,
    required this.meetingTime,
    this.isGroupChat = false,
  }) : super(key: key);

  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isRecording = false;
  String? _currentUserId; // Set this from your auth service

  @override
  void initState() {
    super.initState();
    // Get current user ID from your auth service
    // _currentUserId = authService.currentUser.id;

    // Join chat room
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ChatProvider>(context, listen: false).joinChat(widget.chatId);
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    final content = _messageController.text.trim();
    if (content.isEmpty) return;

    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    chatProvider.sendMessage(content: content);
    _messageController.clear();

    // Scroll to bottom
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _sendAudioMessage(String audioUrl) {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    chatProvider.sendMessage(
      content: 'Audio message',
      isAudio: true,
      audioUrl: audioUrl,
    );
  }

  void _handleTyping(String text) {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    chatProvider.sendTyping(text.isNotEmpty);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(widget.chatTitle),
            Consumer<ChatProvider>(builder: (context, chatProvider, _) {
              final status = chatProvider.chatStatus;
              if (status != null) {
                return Text(
                  status.status,
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(int.parse('0xFF${status.statusColor}')),
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              // Show meeting details
              _showMeetingDetails();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Chat status banner
          Consumer<ChatProvider>(builder: (context, chatProvider, _) {
            final status = chatProvider.chatStatus;
            if (status != null) {
              return Container(
                padding: const EdgeInsets.all(8),
                color: Colors.grey[200],
                child: Text(
                  status.statusMessage,
                  style: const TextStyle(fontSize: 12),
                  textAlign: TextAlign.center,
                ),
              );
            }
            return const SizedBox.shrink();
          }),

          // Messages list
          Expanded(
            child: Consumer<ChatProvider>(builder: (context, chatProvider, _) {
              final messages = chatProvider.messages;
              final typingUsers = chatProvider.typingUsers;

              if (!chatProvider.isConnected) {
                return const Center(
                  child: Text('Connecting to chat server...'),
                );
              }

              if (messages.isEmpty) {
                return const Center(
                  child: Text('No messages yet'),
                );
              }

              return ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(8),
                itemCount: messages.length + (typingUsers.values.contains(true) ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == messages.length) {
                    // Typing indicator
                    return _buildTypingIndicator(typingUsers);
                  }

                  final message = messages[index];
                  return _buildMessageItem(message);
                },
              );
            }),
          ),

          // Message input
          Consumer<ChatProvider>(builder: (context, chatProvider, _) {
            final status = chatProvider.chatStatus;
            final canSendMessage = status?.canSendMessage ?? false;

            if (!canSendMessage) {
              return Container(
                padding: const EdgeInsets.all(16),
                color: Colors.grey[200],
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        status?.statusMessage ?? 'Chat is currently closed',
                        textAlign: TextAlign.center,
                      ),
                    ),
                    if (status?.timeUntilOpen != null)
                      ElevatedButton(
                        onPressed: () {
                          // Show premium upgrade dialog
                          _showPremiumUpgradeDialog(status!.timeUntilOpen!);
                        },
                        child: const Text('Upgrade'),
                      ),
                  ],
                ),
              );
            }

            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Row(
                children: [
                  // Audio recording button
                  IconButton(
                    icon: Icon(_isRecording ? Icons.stop : Icons.mic),
                    onPressed: () {
                      // Toggle recording state
                      setState(() {
                        _isRecording = !_isRecording;
                      });

                      // Implement audio recording logic
                      if (!_isRecording) {
                        // Audio recording stopped, send the audio
                        // _sendAudioMessage(audioUrl);
                      }
                    },
                  ),

                  // Text input
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      decoration: const InputDecoration(
                        hintText: 'Type a message',
                        border: InputBorder.none,
                      ),
                      onChanged: _handleTyping,
                    ),
                  ),

                  // Send button
                  IconButton(
                    icon: const Icon(Icons.send),
                    onPressed: _sendMessage,
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildMessageItem(ChatMessage message) {
    final isCurrentUser = message.sender.id == _currentUserId;
    final isInfo = message.isInfo;

    if (isInfo) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        alignment: Alignment.center,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            message.content,
            style: const TextStyle(fontSize: 12, color: Colors.black54),
          ),
        ),
      );
    }

    return Align(
      alignment: isCurrentUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isCurrentUser ? Colors.blue[100] : Colors.grey[200],
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!isCurrentUser && widget.isGroupChat)
              Text(
                message.sender.name,
                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
              ),
            if (message.isAudio)
              // Audio player widget
              _buildAudioPlayer(message.audioUrl!)
            else
              Text(message.content),
            const SizedBox(height: 4),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  DateFormat('HH:mm').format(message.timestamp),
                  style: const TextStyle(fontSize: 10, color: Colors.black54),
                ),
                if (isCurrentUser) ...[
                  const SizedBox(width: 4),
                  Icon(
                    message.read.values.every((read) => read)
                        ? Icons.done_all
                        : Icons.done,
                    size: 12,
                    color: message.read.values.every((read) => read)
                        ? Colors.blue
                        : Colors.black54,
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAudioPlayer(String audioUrl) {
    // Implement audio player widget
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.play_arrow),
          onPressed: () {
            // Play audio
          },
        ),
        const Text('Audio message'),
      ],
    );
  }

  Widget _buildTypingIndicator(Map<String, bool> typingUsers) {
    final typingUserIds = typingUsers.entries
        .where((entry) => entry.value && entry.key != _currentUserId)
        .map((entry) => entry.key)
        .toList();

    if (typingUserIds.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(8),
      alignment: Alignment.centerLeft,
      child: const Text(
        'Typing...',
        style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
      ),
    );
  }

  void _showMeetingDetails() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Meeting Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Title: ${widget.chatTitle}'),
            const SizedBox(height: 8),
            Text('Time: ${DateFormat('MMM dd, yyyy - HH:mm').format(widget.meetingTime)}'),
            const SizedBox(height: 16),
            Consumer<ChatProvider>(builder: (context, chatProvider, _) {
              final status = chatProvider.chatStatus;
              if (status != null) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Status: ${status.status}'),
                    Text('Chat opens: ${status.chatOpenTime != null ? DateFormat('MMM dd, yyyy - HH:mm').format(status.chatOpenTime!) : 'N/A'}'),
                    Text('Chat closes: ${status.chatCloseTime != null ? DateFormat('MMM dd, yyyy - HH:mm').format(status.chatCloseTime!) : 'N/A'}'),
                  ],
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Show re-invite dialog
              _showReInviteDialog();
            },
            child: const Text('Re-invite'),
          ),
        ],
      ),
    );
  }

  void _showPremiumUpgradeDialog(int hoursUntilOpen) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Upgrade to Premium'),
        content: Text(
          'The chat will open in $hoursUntilOpen hours – want to send a message already?',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to premium upgrade screen
              // Navigator.of(context).pushNamed('/premium-upgrade');
            },
            child: const Text('Upgrade Now'),
          ),
        ],
      ),
    );
  }

  void _showReInviteDialog() {
    final now = DateTime.now();
    final meetingTime = widget.meetingTime;

    // Only show re-invite option if meeting is in the past
    if (meetingTime.isAfter(now)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('This meeting is still upcoming')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Send Invitation'),
        content: const Text(
          'Your invitation is in the past. Want to resend a new one and meet up again?',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to create invitation screen with pre-selected recipients
              // Navigator.of(context).pushNamed('/create-invitation', arguments: {'recipients': widget.participants});
            },
            child: const Text('Yes'),
          ),
        ],
      ),
    );
  }
}
```

## Premium vs Standard User Features

The NetMe chat system has different features for premium and standard users. Here's how to implement these differences:

### 1. Chat Activation Windows

The chat activation window is different for premium and standard users:

- **Standard Users**: Chat opens 3 hours before the meeting and closes 1 hour after
- **Premium Users**: Chat opens 24 hours before the meeting and closes 1 hour after

This is handled by the server, but your Flutter app needs to display the appropriate UI based on the chat status:

```dart
// In your ChatProvider or a dedicated service
class ChatTimingService {
  final bool isPremium;

  ChatTimingService({required this.isPremium});

  DateTime getChatOpenTime(DateTime meetingTime) {
    final openTime = DateTime.fromMillisecondsSinceEpoch(
      meetingTime.millisecondsSinceEpoch
    );

    if (isPremium) {
      return openTime.subtract(const Duration(hours: 24));
    } else {
      return openTime.subtract(const Duration(hours: 3));
    }
  }

  DateTime getChatCloseTime(DateTime meetingTime) {
    return meetingTime.add(const Duration(hours: 1));
  }

  String getActivationMessage(DateTime meetingTime) {
    if (isPremium) {
      return "The chat will open automatically 24 hours before your meetup.";
    } else {
      return "The chat will open automatically 3 hours before your meetup.";
    }
  }

  String getPushNotificationMessage() {
    if (isPremium) {
      return "Your meetup is in 24 hours – the chat is now open for any last-minute coordination.";
    } else {
      return "Your meetup is in 3 hours – the chat is now open for any last-minute coordination.";
    }
  }
}
```

### 2. Message Limitations for Standard Users

Standard users are limited to 2 messages per chat. The server enforces this limit, but your app should handle the error gracefully and show an upgrade prompt:

```dart
// In your ChatProvider
void handleMessageLimitError() {
  // Show premium upgrade dialog
  showDialog(
    context: navigatorKey.currentContext!,
    builder: (context) => AlertDialog(
      title: const Text('Message Limit Reached'),
      content: const Text(
        'You have reached the message limit for standard users. Upgrade to Premium to send unlimited messages.',
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text('Later'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            // Navigate to premium upgrade screen
            Navigator.of(context).pushNamed('/premium-upgrade');
          },
          child: const Text('Upgrade Now'),
        ),
      ],
    ),
  );
}
```

## Group Chat Implementation

Group chats have some special behaviors in the NetMe app:

1. The group chat activates once at least two people have accepted the invitation
2. New joiners can only see messages from the moment they joined
3. Chat activation timing follows the same rules as 1-on-1 chats (3 hours before for standard users, 24 hours before for premium users)

Here's how to implement these features:

### 1. Group Chat Model

Extend your chat models to include group-specific properties:

```dart
class GroupChatInfo {
  final bool isGroupChat;
  final List<String> participants;
  final DateTime joinedAt;

  GroupChatInfo({
    required this.isGroupChat,
    required this.participants,
    required this.joinedAt,
  });

  factory GroupChatInfo.fromJson(Map<String, dynamic> json) {
    return GroupChatInfo(
      isGroupChat: json['isGroupChat'] ?? false,
      participants: List<String>.from(json['participants'] ?? []),
      joinedAt: DateTime.parse(json['joinedAt'] ?? DateTime.now().toIso8601String()),
    );
  }
}
```

### 2. Group Chat UI Modifications

Modify your chat UI to handle group-specific features:

```dart
// In ChatScreen
@override
Widget build(BuildContext context) {
  return Scaffold(
    appBar: AppBar(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(widget.chatTitle),
          if (widget.isGroupChat)
            Text(
              '${widget.participants.length} participants',
              style: TextStyle(fontSize: 12),
            ),
          // Rest of the code...
        ],
      ),
      // Add a button to view all participants
      actions: [
        if (widget.isGroupChat)
          IconButton(
            icon: Icon(Icons.people),
            onPressed: () {
              _showParticipantsList();
            },
          ),
        // Rest of the actions...
      ],
    ),
    // Rest of the UI...
  );
}

void _showParticipantsList() {
  showModalBottomSheet(
    context: context,
    builder: (context) => ListView.builder(
      itemCount: widget.participants.length,
      itemBuilder: (context, index) {
        final participant = widget.participants[index];
        return ListTile(
          leading: CircleAvatar(
            child: Text(participant.name[0]),
          ),
          title: Text(participant.name),
          subtitle: Text(participant.joinedAt != null
            ? 'Joined ${DateFormat.yMMMd().format(participant.joinedAt!)}'
            : ''),
        );
      },
    ),
  );
}
```

### 3. Handling Message History for New Joiners

The server will only send messages from the time the user joined the group chat. Your Flutter app should handle this automatically since it will only display the messages it receives from the server.

## Push Notifications

Implement push notifications for chat messages when users are offline:

### 1. Setup Firebase Cloud Messaging

Add the following dependencies to your `pubspec.yaml`:

```yaml
dependencies:
  firebase_core: ^1.10.0
  firebase_messaging: ^11.2.0
  flutter_local_notifications: ^9.1.5
```

### 2. Create a Notification Service

Create a file `lib/services/notification_service.dart`:

```dart
import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/material.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FirebaseMessaging _fcm = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  Future<void> initialize() async {
    // Request permission
    await _fcm.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );

    // Initialize local notifications
    const AndroidInitializationSettings androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const IOSInitializationSettings iosSettings = IOSInitializationSettings(
      requestSoundPermission: true,
      requestBadgePermission: true,
      requestAlertPermission: true,
    );
    const InitializationSettings initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    await _localNotifications.initialize(
      initSettings,
      onSelectNotification: _onSelectNotification,
    );

    // Handle FCM messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

    // Get FCM token
    final token = await _fcm.getToken();
    print('FCM Token: $token');

    // Send token to your server
    // await apiService.updateFcmToken(token);
  }

  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('Foreground message: ${message.data}');

    // Extract notification data
    final data = message.data;
    final title = message.notification?.title ?? 'New Message';
    final body = message.notification?.body ?? 'You have a new message';

    // Show local notification
    await _showLocalNotification(title, body, data);
  }

  Future<void> _showLocalNotification(String title, String body, Map<String, dynamic> data) async {
    const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'chat_channel',
      'Chat Notifications',
      channelDescription: 'Notifications for chat messages',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );
    const NotificationDetails notificationDetails = NotificationDetails(android: androidDetails);

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title,
      body,
      notificationDetails,
      payload: json.encode(data),
    );
  }

  Future<void> _onSelectNotification(String? payload) async {
    if (payload != null) {
      final data = json.decode(payload) as Map<String, dynamic>;
      _handleNotificationTap(data);
    }
  }

  void _handleNotificationTap(Map<String, dynamic> data) {
    // Navigate to chat screen
    if (data.containsKey('chatId')) {
      final chatId = data['chatId'];
      final chatTitle = data['chatTitle'] ?? 'Chat';
      final meetingTime = data['meetingTime'] != null
          ? DateTime.parse(data['meetingTime'])
          : DateTime.now();
      final isGroupChat = data['isGroupChat'] == 'true';

      navigatorKey.currentState?.pushNamed(
        '/chat',
        arguments: {
          'chatId': chatId,
          'chatTitle': chatTitle,
          'meetingTime': meetingTime,
          'isGroupChat': isGroupChat,
        },
      );
    }
  }

  void _handleMessageOpenedApp(RemoteMessage message) {
    final data = message.data;
    _handleNotificationTap(data);
  }

  // Subscribe to chat topic
  Future<void> subscribeToChatTopic(String chatId) async {
    await _fcm.subscribeToTopic('chat_$chatId');
  }

  // Unsubscribe from chat topic
  Future<void> unsubscribeFromChatTopic(String chatId) async {
    await _fcm.unsubscribeFromTopic('chat_$chatId');
  }
}

// This function must be top-level (not inside a class)
Future<void> _handleBackgroundMessage(RemoteMessage message) async {
  print('Background message: ${message.data}');
  // No UI operations here, just data processing if needed
}
```

### 3. Initialize the Notification Service

In your `main.dart` file:

```dart
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'services/notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp();

  // Initialize notification service
  final notificationService = NotificationService();
  await notificationService.initialize();

  runApp(MyApp(
    navigatorKey: notificationService.navigatorKey,
  ));
}

class MyApp extends StatelessWidget {
  final GlobalKey<NavigatorState> navigatorKey;

  const MyApp({Key? key, required this.navigatorKey}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: navigatorKey,
      // Rest of your app configuration
    );
  }
}
```

### 4. Subscribe to Chat Topics

When a user joins a chat, subscribe them to that chat's topic:

```dart
// In your ChatScreen
@override
void initState() {
  super.initState();

  // Subscribe to chat notifications
  NotificationService().subscribeToChatTopic(widget.chatId);

  // Rest of your initialization
}

@override
void dispose() {
  // Unsubscribe from chat notifications when leaving the chat screen
  NotificationService().unsubscribeFromChatTopic(widget.chatId);

  super.dispose();
}
```

## Troubleshooting

Here are some common issues and their solutions:

### 1. Socket Connection Issues

**Problem**: Socket fails to connect or frequently disconnects

**Solutions**:

- Check that the server URL is correct
- Ensure the authentication token is valid
- Add reconnection logic:

```dart
// In SocketService
IO.Socket createSocket(String serverUrl, String token) {
  return IO.io(serverUrl, <String, dynamic>{
    'transports': ['websocket'],
    'autoConnect': true,
    'reconnection': true,
    'reconnectionDelay': 1000,
    'reconnectionAttempts': 5,
    'auth': {
      'token': token
    }
  });
}
```

### 2. Message Not Sending

**Problem**: Messages appear to send but don't show up for other users

**Solutions**:

- Check that you're properly connected to the socket
- Verify that you're joining the correct chat room
- Add error handling and retry logic:

```dart
// In ChatProvider
Future<void> sendMessageWithRetry({
  required String content,
  bool isAudio = false,
  String? audioUrl,
  bool isInfo = false,
}) async {
  if (_currentChatId == null) return;

  // Create a unique message ID for tracking
  final messageId = DateTime.now().millisecondsSinceEpoch.toString();

  // Add message to pending queue
  _pendingMessages[messageId] = {
    'chatId': _currentChatId!,
    'content': content,
    'isAudio': isAudio,
    'audioUrl': audioUrl,
    'isInfo': isInfo,
    'retryCount': 0,
  };

  // Try to send message
  _trySendMessage(messageId);
}

Future<void> _trySendMessage(String messageId) async {
  final message = _pendingMessages[messageId];
  if (message == null) return;

  // Check connection
  if (!_isConnected) {
    // Schedule retry after reconnection
    _reconnectCallbacks.add(() => _trySendMessage(messageId));
    return;
  }

  try {
    // Send message
    _socketService.sendMessage(
      chatId: message['chatId'],
      content: message['content'],
      isAudio: message['isAudio'],
      audioUrl: message['audioUrl'],
      isInfo: message['isInfo'],
    );

    // Remove from pending queue after successful send
    _pendingMessages.remove(messageId);
  } catch (e) {
    // Increment retry count
    message['retryCount'] = message['retryCount'] + 1;

    // Retry up to 3 times
    if (message['retryCount'] < 3) {
      Future.delayed(Duration(seconds: 2), () => _trySendMessage(messageId));
    } else {
      // Give up after 3 retries
      _pendingMessages.remove(messageId);
      _errorController.add('Failed to send message after multiple attempts');
    }
  }
}
```

### 3. Audio Message Issues

**Problem**: Audio messages fail to upload or play

**Solutions**:

- Ensure you're using the correct MIME type for audio files
- Add progress indicators for uploads
- Implement fallback audio player:

```dart
// In ChatScreen
Widget _buildAudioPlayer(String audioUrl) {
  return FutureBuilder<bool>(
    future: _checkAudioUrl(audioUrl),
    builder: (context, snapshot) {
      if (snapshot.connectionState == ConnectionState.waiting) {
        return const CircularProgressIndicator();
      }

      final isValid = snapshot.data ?? false;

      if (!isValid) {
        return const Text('Audio unavailable');
      }

      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
            onPressed: () {
              // Toggle audio playback
              _toggleAudioPlayback(audioUrl);
            },
          ),
          const Text('Audio message'),
        ],
      );
    },
  );
}

Future<bool> _checkAudioUrl(String url) async {
  try {
    final response = await http.head(Uri.parse(url));
    return response.statusCode == 200;
  } catch (e) {
    return false;
  }
}
```

## API Reference

Here's a reference of all the Socket.IO events used in the chat system:

### Client to Server Events

| Event          | Parameters                                                                                  | Description              |
| -------------- | ------------------------------------------------------------------------------------------- | ------------------------ |
| `join_chat`    | `{ chatId: string }`                                                                        | Join a chat room         |
| `send_message` | `{ chatId: string, content: string, isAudio: boolean, audioUrl?: string, isInfo: boolean }` | Send a message to a chat |
| `typing`       | `{ chatId: string, isTyping: boolean }`                                                     | Send typing indicator    |
| `mark_read`    | `{ chatId: string }`                                                                        | Mark messages as read    |

### Server to Client Events

| Event              | Data                                                                                                                              | Description                   |
| ------------------ | --------------------------------------------------------------------------------------------------------------------------------- | ----------------------------- |
| `connect`          | None                                                                                                                              | Socket connected successfully |
| `disconnect`       | None                                                                                                                              | Socket disconnected           |
| `new_message`      | Message object                                                                                                                    | New message received          |
| `typing_indicator` | `{ chatId: string, userId: string, userName: string, isTyping: boolean }`                                                         | User typing status update     |
| `messages_read`    | `{ chatId: string, userId: string, timestamp: string }`                                                                           | Messages marked as read       |
| `chat_status`      | `{ chatId: string, status: string, statusColor: string, statusMessage: string, canSendMessage: boolean, timeUntilOpen?: number }` | Chat status update            |
| `error`            | `{ message: string, ...additionalData }`                                                                                          | Error message                 |

### Message Object Structure

```json
{
  "_id": "message-id",
  "chatId": "chat-id",
  "content": "Message content",
  "isAudio": false,
  "audioUrl": null,
  "isInfo": false,
  "sender": {
    "_id": "user-id",
    "name": "User Name"
  },
  "timestamp": "2023-05-17T12:34:56.789Z",
  "read": {
    "user-id-1": true,
    "user-id-2": false
  }
}
```

### Chat Status Object Structure

```json
{
  "status": "Open now",
  "statusColor": "39FF14",
  "statusMessage": "Chat is open for coordination.",
  "canSendMessage": true,
  "timeUntilOpen": null,
  "chatOpenTime": "2023-05-16T09:00:00.000Z",
  "chatCloseTime": "2023-05-17T13:00:00.000Z"
}
```
