# Group Meeting Feedback API Guide

## Overview

This guide provides comprehensive documentation for implementing group meeting feedback functionality in your Flutter application. The system allows participants to report no-shows after group meetings, with automatic strike application when at least 2 users report the same person.

## API Endpoints

### 1. Submit Group Feedback

**Endpoint:** `POST /api/user/group-feedback/:invitationId`

**Description:** Submit feedback for a group meeting, indicating whether everyone attended or reporting specific no-shows.

**Headers:**

```
Authorization: Bearer <your_jwt_token>
Content-Type: application/json
```

**Path Parameters:**

- `invitationId` (string, required): The ID of the group meeting invitation

**Request Body:**

```json
{
  "allAttended": false,
  "unAttendeUsers": ["userId1", "userId2"]
}
```

**Request Body Fields:**

- `allAttended` (boolean, required): `true` if everyone attended, `false` if someone didn't show up
- `unAttendeUsers` (array, optional): Array of user IDs who didn't show up. Only required when `allAttended` is `false`

**Success Response (201):**

```json
{
  "message": "Group feedback submitted successfully",
  "feedback": {
    "_id": "feedback_id",
    "invitationId": "invitation_id",
    "reportedBy": "reporter_user_id",
    "allAttended": false,
    "unAttendeUsers": ["userId1", "userId2"],
    "submittedAt": "2024-01-15T10:30:00.000Z",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

**Error Responses:**

400 - Validation Error:

```json
{
  "errors": [
    {
      "msg": "allAttended must be a boolean value",
      "param": "allAttended",
      "location": "body"
    }
  ]
}
```

400 - Already Submitted:

```json
{
  "error": "You have already submitted feedback for this meeting"
}
```

400 - Not Group Meeting:

```json
{
  "error": "This endpoint is only for group meetings"
}
```

403 - Unauthorized:

```json
{
  "error": "You are not authorized to provide feedback for this meeting"
}
```

404 - Not Found:

```json
{
  "error": "Invitation not found"
}
```

### 2. Get Group Feedback Status

**Endpoint:** `GET /api/user/group-feedback/:invitationId`

**Description:** Get the current feedback status for a group meeting, including whether the user has submitted feedback and list of confirmed attendees.

**Headers:**

```
Authorization: Bearer <your_jwt_token>
```

**Path Parameters:**

- `invitationId` (string, required): The ID of the group meeting invitation

**Success Response (200):**

```json
{
  "hasSubmittedFeedback": false,
  "userFeedback": null,
  "totalFeedbackCount": 2,
  "confirmedAttendees": [
    {
      "_id": "user1_id",
      "userName": "John Doe"
    },
    {
      "_id": "user2_id",
      "userName": "Jane Smith"
    }
  ],
  "invitationDetails": {
    "_id": "invitation_id",
    "groupName": "Team Meeting",
    "date": "2024-01-15T10:00:00.000Z",
    "time": "10:00",
    "isGroup": true
  }
}
```

**Response Fields:**

- `hasSubmittedFeedback` (boolean): Whether the current user has submitted feedback
- `userFeedback` (object|null): The user's feedback if already submitted
- `totalFeedbackCount` (number): Total number of feedback submissions for this meeting
- `confirmedAttendees` (array): List of users who confirmed attendance (excluding current user)
- `invitationDetails` (object): Basic meeting information

**Error Responses:** Same as submit endpoint (400, 403, 404)

## Postman Collection

### Environment Variables

Create a Postman environment with these variables:

```
base_url: http://localhost:5000/api
auth_token: <your_jwt_token>
invitation_id: <test_invitation_id>
```

### Collection Structure

#### 1. Submit Group Feedback - All Attended

```
Method: POST
URL: {{base_url}}/user/group-feedback/{{invitation_id}}
Headers:
  Authorization: Bearer {{auth_token}}
  Content-Type: application/json
Body (raw JSON):
{
  "allAttended": true
}
```

#### 2. Submit Group Feedback - With No-Shows

```
Method: POST
URL: {{base_url}}/user/group-feedback/{{invitation_id}}
Headers:
  Authorization: Bearer {{auth_token}}
  Content-Type: application/json
Body (raw JSON):
{
  "allAttended": false,
  "unAttendeUsers": ["USER_ID_1", "USER_ID_2"]
}
```

#### 3. Get Group Feedback Status

```
Method: GET
URL: {{base_url}}/user/group-feedback/{{invitation_id}}
Headers:
  Authorization: Bearer {{auth_token}}
```

### Test Scripts

Add this to the "Tests" tab of your requests:

```javascript
// For Submit Group Feedback
pm.test("Status code is 201", function () {
  pm.response.to.have.status(201);
});

pm.test("Response has feedback object", function () {
  const jsonData = pm.response.json();
  pm.expect(jsonData).to.have.property("feedback");
  pm.expect(jsonData.feedback).to.have.property("_id");
});

// For Get Group Feedback Status
pm.test("Status code is 200", function () {
  pm.response.to.have.status(200);
});

pm.test("Response has required fields", function () {
  const jsonData = pm.response.json();
  pm.expect(jsonData).to.have.property("hasSubmittedFeedback");
  pm.expect(jsonData).to.have.property("confirmedAttendees");
  pm.expect(jsonData).to.have.property("invitationDetails");
});
```

## Flutter Implementation Guide

### 1. Data Models

Create these models in your Flutter app:

```dart
// models/group_feedback.dart
class GroupFeedback {
  final String id;
  final String invitationId;
  final String reportedBy;
  final bool allAttended;
  final List<String> unAttendeUsers;
  final DateTime submittedAt;

  GroupFeedback({
    required this.id,
    required this.invitationId,
    required this.reportedBy,
    required this.allAttended,
    required this.unAttendeUsers,
    required this.submittedAt,
  });

  factory GroupFeedback.fromJson(Map<String, dynamic> json) {
    return GroupFeedback(
      id: json['_id'],
      invitationId: json['invitationId'],
      reportedBy: json['reportedBy'],
      allAttended: json['allAttended'],
      unAttendeUsers: List<String>.from(json['unAttendeUsers'] ?? []),
      submittedAt: DateTime.parse(json['submittedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'allAttended': allAttended,
      'unAttendeUsers': unAttendeUsers,
    };
  }
}

// models/group_feedback_status.dart
class GroupFeedbackStatus {
  final bool hasSubmittedFeedback;
  final GroupFeedback? userFeedback;
  final int totalFeedbackCount;
  final List<User> confirmedAttendees;
  final InvitationDetails invitationDetails;

  GroupFeedbackStatus({
    required this.hasSubmittedFeedback,
    this.userFeedback,
    required this.totalFeedbackCount,
    required this.confirmedAttendees,
    required this.invitationDetails,
  });

  factory GroupFeedbackStatus.fromJson(Map<String, dynamic> json) {
    return GroupFeedbackStatus(
      hasSubmittedFeedback: json['hasSubmittedFeedback'],
      userFeedback: json['userFeedback'] != null
          ? GroupFeedback.fromJson(json['userFeedback'])
          : null,
      totalFeedbackCount: json['totalFeedbackCount'],
      confirmedAttendees: (json['confirmedAttendees'] as List)
          .map((user) => User.fromJson(user))
          .toList(),
      invitationDetails: InvitationDetails.fromJson(json['invitationDetails']),
    );
  }
}

class User {
  final String id;
  final String userName;

  User({required this.id, required this.userName});

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['_id'],
      userName: json['userName'],
    );
  }
}

class InvitationDetails {
  final String id;
  final String? groupName;
  final DateTime date;
  final String time;
  final bool isGroup;

  InvitationDetails({
    required this.id,
    this.groupName,
    required this.date,
    required this.time,
    required this.isGroup,
  });

  factory InvitationDetails.fromJson(Map<String, dynamic> json) {
    return InvitationDetails(
      id: json['_id'],
      groupName: json['groupName'],
      date: DateTime.parse(json['date']),
      time: json['time'],
      isGroup: json['isGroup'],
    );
  }
}
```

### 2. API Service

```dart
// services/group_feedback_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;

class GroupFeedbackService {
  final String baseUrl;
  final String authToken;

  GroupFeedbackService({
    required this.baseUrl,
    required this.authToken,
  });

  Map<String, String> get _headers => {
    'Authorization': 'Bearer $authToken',
    'Content-Type': 'application/json',
  };

  Future<GroupFeedback> submitGroupFeedback({
    required String invitationId,
    required bool allAttended,
    List<String>? unAttendeUsers,
  }) async {
    final url = Uri.parse('$baseUrl/user/group-feedback/$invitationId');

    final body = {
      'allAttended': allAttended,
      if (!allAttended && unAttendeUsers != null) 'unAttendeUsers': unAttendeUsers,
    };

    final response = await http.post(
      url,
      headers: _headers,
      body: jsonEncode(body),
    );

    if (response.statusCode == 201) {
      final data = jsonDecode(response.body);
      return GroupFeedback.fromJson(data['feedback']);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to submit feedback');
    }
  }

  Future<GroupFeedbackStatus> getGroupFeedbackStatus(String invitationId) async {
    final url = Uri.parse('$baseUrl/user/group-feedback/$invitationId');

    final response = await http.get(url, headers: _headers);

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return GroupFeedbackStatus.fromJson(data);
    } else {
      final error = jsonDecode(response.body);
      throw Exception(error['error'] ?? 'Failed to get feedback status');
    }
  }
}
```

### 3. UI Implementation

#### Group Feedback Screen

```dart
// screens/group_feedback_screen.dart
import 'package:flutter/material.dart';

class GroupFeedbackScreen extends StatefulWidget {
  final String invitationId;

  const GroupFeedbackScreen({
    Key? key,
    required this.invitationId,
  }) : super(key: key);

  @override
  _GroupFeedbackScreenState createState() => _GroupFeedbackScreenState();
}

class _GroupFeedbackScreenState extends State<GroupFeedbackScreen> {
  final GroupFeedbackService _feedbackService = GroupFeedbackService(
    baseUrl: 'YOUR_API_BASE_URL',
    authToken: 'YOUR_AUTH_TOKEN',
  );

  GroupFeedbackStatus? _feedbackStatus;
  bool _isLoading = true;
  bool? _allAttended;
  Set<String> _selectedNoShows = {};

  @override
  void initState() {
    super.initState();
    _loadFeedbackStatus();
  }

  Future<void> _loadFeedbackStatus() async {
    try {
      final status = await _feedbackService.getGroupFeedbackStatus(widget.invitationId);
      setState(() {
        _feedbackStatus = status;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorDialog('Failed to load feedback status: $e');
    }
  }

  Future<void> _submitFeedback() async {
    if (_allAttended == null) {
      _showErrorDialog('Please select whether everyone attended');
      return;
    }

    if (_allAttended == false && _selectedNoShows.isEmpty) {
      _showErrorDialog('Please select who didn\'t show up');
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });

      await _feedbackService.submitGroupFeedback(
        invitationId: widget.invitationId,
        allAttended: _allAttended!,
        unAttendeUsers: _allAttended! ? null : _selectedNoShows.toList(),
      );

      _showSuccessDialog('Feedback submitted successfully');
      _loadFeedbackStatus(); // Refresh status
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorDialog('Failed to submit feedback: $e');
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Success'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context); // Go back to previous screen
            },
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: Text('Meeting Feedback')),
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (_feedbackStatus == null) {
      return Scaffold(
        appBar: AppBar(title: Text('Meeting Feedback')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('Failed to load meeting information'),
              ElevatedButton(
                onPressed: _loadFeedbackStatus,
                child: Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (_feedbackStatus!.hasSubmittedFeedback) {
      return _buildAlreadySubmittedView();
    }

    return _buildFeedbackForm();
  }

  Widget _buildAlreadySubmittedView() {
    final feedback = _feedbackStatus!.userFeedback!;

    return Scaffold(
      appBar: AppBar(title: Text('Meeting Feedback')),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Feedback Already Submitted',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    SizedBox(height: 16),
                    Text('Meeting: ${_feedbackStatus!.invitationDetails.groupName ?? 'Group Meeting'}'),
                    Text('Date: ${_feedbackStatus!.invitationDetails.date.toString().split(' ')[0]}'),
                    Text('Time: ${_feedbackStatus!.invitationDetails.time}'),
                    SizedBox(height: 16),
                    Text(
                      feedback.allAttended
                          ? 'You reported that everyone attended'
                          : 'You reported ${feedback.unAttendeUsers.length} no-show(s)',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    if (!feedback.allAttended && feedback.unAttendeUsers.isNotEmpty) ...[
                      SizedBox(height: 8),
                      Text('Reported no-shows: ${feedback.unAttendeUsers.length} user(s)'),
                    ],
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Total feedback submissions: ${_feedbackStatus!.totalFeedbackCount}',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeedbackForm() {
    return Scaffold(
      appBar: AppBar(title: Text('Meeting Feedback')),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _feedbackStatus!.invitationDetails.groupName ?? 'Group Meeting',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    Text('Date: ${_feedbackStatus!.invitationDetails.date.toString().split(' ')[0]}'),
                    Text('Time: ${_feedbackStatus!.invitationDetails.time}'),
                  ],
                ),
              ),
            ),
            SizedBox(height: 24),
            Text(
              'Did everyone who confirmed the invitation show up?',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 16),
            RadioListTile<bool>(
              title: Text('Yes, all were there'),
              value: true,
              groupValue: _allAttended,
              onChanged: (value) {
                setState(() {
                  _allAttended = value;
                  _selectedNoShows.clear();
                });
              },
            ),
            RadioListTile<bool>(
              title: Text('No, someone didn\'t show up'),
              value: false,
              groupValue: _allAttended,
              onChanged: (value) {
                setState(() {
                  _allAttended = value;
                });
              },
            ),
            if (_allAttended == false) ...[
              SizedBox(height: 24),
              Text(
                'Who didn\'t show up?',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: _feedbackStatus!.confirmedAttendees.length,
                  itemBuilder: (context, index) {
                    final user = _feedbackStatus!.confirmedAttendees[index];
                    return CheckboxListTile(
                      title: Text(user.userName),
                      value: _selectedNoShows.contains(user.id),
                      onChanged: (checked) {
                        setState(() {
                          if (checked == true) {
                            _selectedNoShows.add(user.id);
                          } else {
                            _selectedNoShows.remove(user.id);
                          }
                        });
                      },
                    );
                  },
                ),
              ),
            ],
            SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _allAttended != null ? _submitFeedback : null,
                child: Text('Submit Feedback'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```

### 4. Integration Example

```dart
// How to navigate to the feedback screen after a meeting
void _showFeedbackDialog(String invitationId) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text('Meeting Feedback'),
      content: Text('Would you like to provide feedback for this meeting?'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Later'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.pop(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => GroupFeedbackScreen(
                  invitationId: invitationId,
                ),
              ),
            );
          },
          child: Text('Provide Feedback'),
        ),
      ],
    ),
  );
}
```

## Business Logic Summary

### Strike System

- Users get strikes when reported by **at least 2 different users** in the same group meeting
- 3 strikes = Warning email
- 5 strikes = Account deactivation
- Only confirmed attendees can be reported
- Users cannot report themselves

### Key Features

1. **Duplicate Prevention**: Users can only submit feedback once per meeting
2. **Validation**: Only group meeting participants can submit feedback
3. **Threshold-based Strikes**: Requires multiple reports for strike application
4. **Real-time Status**: Check if feedback was already submitted
5. **Comprehensive Logging**: All actions are logged for debugging

### Error Handling

- Proper validation for all inputs
- Clear error messages for different scenarios
- Graceful handling of network errors
- User-friendly feedback for all operations

This implementation provides a complete group meeting feedback system that matches WhatsApp-like behavior while ensuring fair strike application through the 2-user threshold requirement.
