# NETME Chat - Firebase Configuration for Flutter

This document provides the necessary Firebase configuration and API endpoints for implementing the chat functionality in the Flutter app.

## Firebase Configuration

### 1. Add Firebase to your Flutter project

First, add Firebase to your Flutter project by following the [official documentation](https://firebase.flutter.dev/docs/overview).

```yaml
# pubspec.yaml
dependencies:
  flutter:
    sdk: flutter
  firebase_core: ^2.15.0
  firebase_auth: ^4.7.2
  cloud_firestore: ^4.8.4
  firebase_messaging: ^14.6.5
  flutter_local_notifications: ^14.1.1
```

### 2. Initialize Firebase in your Flutter app

```dart
// main.dart
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();

  // Set up Firebase Messaging
  FirebaseMessaging messaging = FirebaseMessaging.instance;

  // Request permission for notifications
  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    badge: true,
    sound: true,
  );

  // Initialize local notifications
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // Initialize settings for Android
  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings('@mipmap/ic_launcher');

  // Initialize settings for iOS
  const DarwinInitializationSettings initializationSettingsIOS =
      DarwinInitializationSettings();

  const InitializationSettings initializationSettings = InitializationSettings(
    android: initializationSettingsAndroid,
    iOS: initializationSettingsIOS,
  );

  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
  );

  // Get FCM token and send to backend
  String? token = await messaging.getToken();
  if (token != null) {
    // Send token to your backend
    // Use the updateFcmToken API endpoint
  }

  // Handle background messages
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  runApp(MyApp());
}

// Handle background messages
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print("Handling a background message: ${message.messageId}");
}
```

## Firestore Collections Structure

The chat system uses the following Firestore collections:

1. **chats**: Stores chat metadata

   - `invitationId`: ID of the meeting
   - `participants`: Array of user IDs
   - `isGroupChat`: Boolean indicating if it's a group chat
   - `chatOpenTime`: Timestamp when chat opens
   - `chatCloseTime`: Timestamp when chat closes
   - `hasPremiumUser`: Boolean indicating if any participant is premium
   - `premiumEarlyStart`: Boolean indicating if a premium user started the chat early
   - `lastMessage`: Object containing the last message details
   - `active`: Boolean indicating if the chat is active
   - `createdAt`: Timestamp when the chat was created
   - `updatedAt`: Timestamp when the chat was last updated

2. **messages**: Stores individual messages
   - `chatId`: ID of the chat
   - `senderId`: ID of the message sender
   - `content`: Message content
   - `timestamp`: Timestamp when the message was sent
   - `read`: Array of objects with user IDs and read timestamps

## API Endpoints

Use these endpoints to interact with the chat functionality:

### 1. Initialize Chat

```
POST /api/chat/initialize/:invitationId
```

**Description**: Creates a new chat for a meeting or returns an existing one.

**Authentication**: Required

**Response**:

```json
{
  "success": true,
  "chat": {
    "id": "chatId",
    "invitationId": "invitationId",
    "participants": ["userId1", "userId2"],
    "isGroupChat": false,
    "chatOpenTime": "2023-05-16T12:00:00Z",
    "chatCloseTime": "2023-05-16T16:00:00Z",
    "hasPremiumUser": true,
    "active": true,
    "createdAt": "2023-05-15T12:00:00Z",
    "updatedAt": "2023-05-15T12:00:00Z"
  },
  "status": {
    "status": "Open soon",
    "color": "gray",
    "message": "The chat will open automatically 24 hours before your meetup.",
    "chatOpenTime": "2023-05-16T12:00:00Z",
    "chatCloseTime": "2023-05-16T16:00:00Z",
    "isPremium": true,
    "canSendMessage": false
  }
}
```

### 2. Get Chat Status

```
GET /api/chat/status/:chatId
```

**Description**: Gets the current status of a chat.

**Authentication**: Required

**Response**:

```json
{
  "success": true,
  "status": {
    "status": "Open now",
    "color": "neon green",
    "message": "Chat is open for coordination.",
    "chatOpenTime": "2023-05-16T12:00:00Z",
    "chatCloseTime": "2023-05-16T16:00:00Z",
    "isPremium": false,
    "canSendMessage": true
  }
}
```

### 3. Get User's Chats

```
GET /api/chat/user-chats
```

**Description**: Gets all chats for the authenticated user.

**Authentication**: Required

**Response**:

```json
{
  "success": true,
  "chats": [
    {
      "_id": "chatId1",
      "invitationId": "meetingId1",
      "meetingTitle": "Coffee Meetup",
      "scheduledTime": "2023-05-16T15:00:00Z",
      "location": {
        /* location object */
      },
      "isGroupChat": false,
      "participants": [
        {
          "_id": "userId2",
          "name": "John Doe",
          "profilePicture": "url_to_profile_picture"
        }
      ],
      "status": "Open now",
      "statusColor": "neon green",
      "statusMessage": "Chat is open for coordination.",
      "unreadCount": 2,
      "lastMessage": {
        "content": "See you soon!",
        "sender": "userId2",
        "timestamp": "2023-05-16T14:30:00Z"
      },
      "chatOpenTime": "2023-05-16T12:00:00Z",
      "chatCloseTime": "2023-05-16T16:00:00Z"
    }
  ]
}
```

### 4. Get Chat Messages

```
GET /api/chat/messages/:chatId
```

**Description**: Gets messages for a specific chat and marks them as read.

**Authentication**: Required

**Response**:

```json
{
  "success": true,
  "chatId": "chatId",
  "status": {
    "status": "Open now",
    "color": "neon green",
    "message": "Chat is open for coordination.",
    "chatOpenTime": "2023-05-16T12:00:00Z",
    "chatCloseTime": "2023-05-16T16:00:00Z",
    "isPremium": false,
    "canSendMessage": true
  }
}
```

### 5. SendMessage

```
POST /api/chat/messages/:chatId
```

**Description**: SendMessage to other participants.

**Authentication**: Required

**Post Body**

```json
{
  "content": "Your message here"
}
```

**Response**:

```json
{
  "success": true,
  "message": "Message sent successfully",
  "data": {
    "id": "ftkuqFz4jYsmFFV0TMlh",
    "chat": "Your message here",
    "fromId": "67300213287cd17f25e533c4",
    "isAudio": false,
    "isInfo": false,
    "isNewUser": false,
    "timestamp": 1747418164455000,
    "read": {
      "67300213287cd17f25e533c4": true,
      "672cdc6317f96d9b609cb4fb": false
    },
    "numberOfUnread": {
      "67300213287cd17f25e533c4": 0,
      "672cdc6317f96d9b609cb4fb": 1
    },
    "chatId": "68277277f02f4a9d3649968f",
    "users": ["67300213287cd17f25e533c4", "672cdc6317f96d9b609cb4fb"]
  }
}
```

### 6. Get Chat Data for Re-Invite

```
GET /api/chat/re-invite/:chatId
```

**Description**: Gets chat data for re-inviting participants.

**Authentication**: Required

**Response**:

```json
{
  "success": true,
  "reInviteData": {
    "chatId": "chatId",
    "participants": [
      {
        "_id": "userId1",
        "name": "Jane Doe",
        "profilePicture": "url_to_profile_picture",
        "location": {
          /* location object */
        }
      },
      {
        "_id": "userId2",
        "name": "John Doe",
        "profilePicture": "url_to_profile_picture",
        "location": {
          /* location object */
        }
      }
    ],
    "isGroupChat": false
  }
}
```

## Flutter Implementation Guide

### 1. Chat List Screen

Implement a screen that displays all chats for the user with appropriate status tags:

```dart
class ChatListScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Chats')),
      body: FutureBuilder<http.Response>(
        future: getUserChats(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }

          final data = jsonDecode(snapshot.data!.body);
          final chats = data['chats'];

          return ListView.builder(
            itemCount: chats.length,
            itemBuilder: (context, index) {
              final chat = chats[index];
              return ChatListItem(
                chat: chat,
                onTap: () => navigateToChatDetail(context, chat),
              );
            },
          );
        },
      ),
    );
  }

  Future<http.Response> getUserChats() async {
    final token = await getAuthToken();
    return http.get(
      Uri.parse('https://your-api.com/api/chat/user-chats'),
      headers: {
        'Authorization': 'Bearer $token',
      },
    );
  }

  void navigateToChatDetail(BuildContext context, Map<String, dynamic> chat) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatDetailScreen(chatId: chat['_id']),
      ),
    );
  }
}

class ChatListItem extends StatelessWidget {
  final Map<String, dynamic> chat;
  final VoidCallback onTap;

  const ChatListItem({required this.chat, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: CircleAvatar(
        backgroundImage: NetworkImage(chat['participants'][0]['profilePicture']),
      ),
      title: Text(chat['meetingTitle']),
      subtitle: Text(chat['lastMessage'] != null
        ? chat['lastMessage']['content']
        : 'No messages yet'),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildStatusTag(chat['status'], chat['statusColor']),
          if (chat['unreadCount'] > 0)
            Container(
              padding: EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: Text(
                '${chat['unreadCount']}',
                style: TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
        ],
      ),
      onTap: onTap,
    );
  }

  Widget _buildStatusTag(String status, String color) {
    Color tagColor;
    switch (color) {
      case 'neon green':
        tagColor = Colors.green;
        break;
      case 'gray':
        tagColor = Colors.grey;
        break;
      case 'red':
        tagColor = Colors.red;
        break;
      default:
        tagColor = Colors.blue;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: tagColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status,
        style: TextStyle(color: Colors.white, fontSize: 12),
      ),
    );
  }
}
```

### 2. Chat Detail Screen

Implement a screen that displays chat messages and allows sending new messages:

```dart
class ChatDetailScreen extends StatefulWidget {
  final String chatId;

  const ChatDetailScreen({required this.chatId});

  @override
  _ChatDetailScreenState createState() => _ChatDetailScreenState();
}

class _ChatDetailScreenState extends State<ChatDetailScreen> {
  final TextEditingController _messageController = TextEditingController();
  late Stream<QuerySnapshot> _messagesStream;
  Map<String, dynamic>? _chatStatus;

  @override
  void initState() {
    super.initState();
    _fetchChatStatus();
    _initMessagesStream();
  }

  Future<void> _fetchChatStatus() async {
    final token = await getAuthToken();
    final response = await http.get(
      Uri.parse('https://your-api.com/api/chat/status/${widget.chatId}'),
      headers: {
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      setState(() {
        _chatStatus = data['status'];
      });
    }
  }

  void _initMessagesStream() {
    _messagesStream = FirebaseFirestore.instance
        .collection('messages')
        .where('chatId', isEqualTo: widget.chatId)
        .orderBy('timestamp')
        .snapshots();

    // Mark messages as read
    http.get(
      Uri.parse('https://your-api.com/api/chat/messages/${widget.chatId}'),
      headers: {
        'Authorization': 'Bearer $token',
      },
    );
  }

  Future<void> _sendMessage() async {
    if (_messageController.text.trim().isEmpty) return;

    final userId = await getCurrentUserId();

    try {
      // Add message to Firestore
      await FirebaseFirestore.instance.collection('messages').add({
        'chatId': widget.chatId,
        'senderId': userId,
        'content': _messageController.text.trim(),
        'timestamp': FieldValue.serverTimestamp(),
      });

      _messageController.clear();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error sending message: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Chat'),
        actions: [
          IconButton(
            icon: Icon(Icons.email),
            onPressed: () => _showMeetingDetails(),
          ),
        ],
      ),
      body: Column(
        children: [
          if (_chatStatus != null && !_chatStatus!['canSendMessage'])
            Container(
              padding: EdgeInsets.all(8),
              color: _getChatStatusColor(_chatStatus!['color']),
              child: Text(
                _chatStatus!['message'],
                style: TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
            ),
          Expanded(
            child: StreamBuilder<QuerySnapshot>(
              stream: _messagesStream,
              builder: (context, snapshot) {
                if (snapshot.hasError) {
                  return Center(child: Text('Error: ${snapshot.error}'));
                }

                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: CircularProgressIndicator());
                }

                final messages = snapshot.data!.docs;

                return ListView.builder(
                  reverse: true,
                  itemCount: messages.length,
                  itemBuilder: (context, index) {
                    final message = messages[messages.length - 1 - index].data() as Map<String, dynamic>;
                    return MessageBubble(
                      message: message,
                      isMe: message['senderId'] == getCurrentUserId(),
                    );
                  },
                );
              },
            ),
          ),
          if (_chatStatus != null && _chatStatus!['canSendMessage'])
            _buildMessageInput()
          else if (_chatStatus != null && !_chatStatus!['isPremium'] &&
                  _chatStatus!['status'] == 'Open soon')
            _buildPremiumUpsell(),
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'Type a message...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
              ),
            ),
          ),
          IconButton(
            icon: Icon(Icons.send),
            onPressed: _sendMessage,
          ),
        ],
      ),
    );
  }

  Widget _buildPremiumUpsell() {
    if (_chatStatus == null) return SizedBox();

    final hoursUntilOpen = _chatStatus!['chatOpenTime'] != null
        ? DateTime.parse(_chatStatus!['chatOpenTime']).difference(DateTime.now()).inHours
        : 0;

    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          Text(
            'The chat will open in $hoursUntilOpen hours – want to send a message already?',
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 12),
          ElevatedButton(
            child: Text('Upgrade to Premium'),
            onPressed: () {
              // Navigate to premium upgrade screen
            },
          ),
        ],
      ),
    );
  }

  void _showMeetingDetails() async {
    // Show meeting details and re-invite option if chat is closed
    if (_chatStatus != null && _chatStatus!['status'] == 'Closed') {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Send Invitation'),
          content: Text('Your invitation is in the past. Want to resend a new one and meet up again?'),
          actions: [
            TextButton(
              child: Text('Later'),
              onPressed: () => Navigator.pop(context),
            ),
            TextButton(
              child: Text('Yes'),
              onPressed: () {
                Navigator.pop(context);
                _navigateToReInvite();
              },
            ),
          ],
        ),
      );
    } else {
      // Show meeting details
    }
  }

  void _navigateToReInvite() async {
    final token = await getAuthToken();
    final response = await http.get(
      Uri.parse('https://your-api.com/api/chat/re-invite/${widget.chatId}'),
      headers: {
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      // Navigate to re-invite screen with data['reInviteData']
    }
  }

  Color _getChatStatusColor(String color) {
    switch (color) {
      case 'neon green':
        return Colors.green;
      case 'gray':
        return Colors.grey;
      case 'red':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }
}

class MessageBubble extends StatelessWidget {
  final Map<String, dynamic> message;
  final bool isMe;

  const MessageBubble({required this.message, required this.isMe});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isMe ? Colors.blue : Colors.grey[300],
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          message['content'],
          style: TextStyle(
            color: isMe ? Colors.white : Colors.black,
          ),
        ),
      ),
    );
  }
}
```

## Handling Chat Rules

The backend handles all chat rules automatically. The Flutter app should:

1. Display appropriate status messages based on the chat status
2. Show premium upsell when a standard user tries to send a message too early
3. Handle error responses from Firestore when a message is rejected
4. Display chat tags (Open now, Open soon, Closed) with appropriate colors

## Error Handling

When sending messages, handle these potential errors:

1. "Chat not open yet" - Show premium upsell
2. "Message limit reached" - Show premium upsell
3. "Chat is closed" - Disable message input
4. "Group chat requires at least two accepted participants" - Show appropriate message

## Push Notifications

The backend will send push notifications for:

1. Chat opening (different messages for standard and premium users)
2. New messages
3. Premium users starting chat early

Make sure to handle these notifications appropriately in your Flutter app.
