const Voucher = require("../models/Admin/voucher");

// const checkVoucher = async (code) => {
//   try {
//     const voucher = await Voucher.findOne({ code });
//     if (!voucher) {
//       return { valid: false, message: "Invalid voucher code" };
//     }

//     const now = new Date();
//     const condition =
//       voucher.active ||
//       now > voucher.startDate ||
//       now < voucher.endDate ||
//       (voucher.expiryDate ? now < voucher.expiryDate : true);
//     console.log("condition", condition);
//     if (!condition) {
//       return { valid: false, message: "Voucher code is not active" };
//     }
//     return { valid: true, discount: voucher.percentage };
//   } catch (error) {
//     console.error("Error checking voucher:", error);
//     return { valid: false, message: "Error checking voucher" };
//   }
// };

const checkVoucher = async (code) => {
  try {
    const voucher = await Voucher.findOne({ code });
    if (!voucher) {
      return { valid: false, message: "Invalid voucher code" };
    }

    const now = new Date();

    // Adjust date comparisons to be inclusive of the whole day for startDate and endDate
    const startOfToday = new Date(
      now.toISOString().split("T")[0] + "T00:00:00.000Z"
    );
    const endOfToday = new Date(
      now.toISOString().split("T")[0] + "T23:59:59.999Z"
    );
    console.log("startOfToday", startOfToday);
    console.log("endOfToday", endOfToday);
    const isActive = voucher.active;
    const isWithinStartDate = startOfToday >= new Date(voucher.startDate); // Inclusive of start date
    const isWithinEndDate =
      !voucher.endDate || endOfToday <= new Date(voucher.endDate); // Inclusive of end date
    const isWithinExpiryDate =
      !voucher.expiryDate || now <= new Date(voucher.expiryDate); // Valid until expiryDate if defined

    console.log("isActive", isActive);
    console.log("isWithinStartDate", isWithinStartDate);
    console.log("isWithinEndDate", isWithinEndDate);
    console.log("isWithinExpiryDate", isWithinExpiryDate);
    const condition =
      isActive && isWithinStartDate && isWithinEndDate && isWithinExpiryDate;
    console.log("condition", condition);
    if (!condition) {
      return { valid: false, message: "Voucher code is not valid or active" };
    }

    return { valid: true, discount: voucher.percentage };
  } catch (error) {
    console.error("Error checking voucher:", error);
    return { valid: false, message: "Error checking voucher" };
  }
};

module.exports = { checkVoucher };
