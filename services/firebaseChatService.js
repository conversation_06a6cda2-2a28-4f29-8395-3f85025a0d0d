// const { db, messaging } = require("../config/firebase");
// const User = require("../models/User/User");
// const Chat = require("../models/Common/Chat");
// const moment = require("moment-timezone");
// const Invitation = require("../models/User/invitations");

// /**
//  * Firebase Chat Service
//  * Handles all Firebase Cloud Functions related to chat functionality
//  */
// class FirebaseChatService {
//   /**
//    * Initialize Firestore collections
//    */
//   constructor() {
//     this.chatsCollection = db.collection("chats");
//     this.messagesCollection = db.collection("messages");
//     this.userStatusCollection = db.collection("userStatus");
//   }

//   /**
//    * Set up Firebase Cloud Functions triggers
//    * This should be called when the server starts
//    */
//   setupTriggers() {
//     // Set up message creation trigger in Firestore
//     this.messagesCollection.onWrite(async (change, context) => {
//       try {
//         if (!change.before.exists) {
//           // New message created
//           const messageData = change.after.data();
//           return this.validateAndProcessMessage(messageData);
//         }
//         return null;
//       } catch (error) {
//         console.error("Error in message trigger:", error);
//         return null;
//       }
//     });

//     // Set up chat status trigger
//     this.chatsCollection.onWrite(async (change, context) => {
//       try {
//         if (!change.before.exists) {
//           // New chat created
//           const chatData = change.after.data();
//           return this.setupChatTimeWindow(chatData);
//         }
//         return null;
//       } catch (error) {
//         console.error("Error in chat trigger:", error);
//         return null;
//       }
//     });
//   }

//   /**
//    * Create a new chat for a meeting
//    * @param {Object} meetingData - Meeting data
//    * @returns {Promise<Object>} - Created chat data
//    */
//   async createChat(meetingData) {
//     try {
//       const {
//         _id: invitationId,
//         participants,
//         scheduledTime,
//         isGroupMeeting,
//       } = meetingData;

//       // Create chat document in Firestore
//       // const chatRef = this.chatsCollection.doc(invitationId.toString());

//       // Get user roles
//       const participantIds = participants.map((p) => p.toString());
//       const users = await User.find({ _id: { $in: participantIds } }).select(
//         "_id isPremium"
//       );

//       // Calculate chat open and close times based on user roles
//       const hasPremiumUser = users.some((user) => user.isPremium);
//       const meetingTime = moment(scheduledTime);

//       // Premium users: chat opens 24 hours before, Standard: 3 hours before
//       // Both close 1 hour after meeting
//       const chatOpenTime = hasPremiumUser
//         ? meetingTime.clone().subtract(24, "hours")
//         : meetingTime.clone().subtract(3, "hours");

//       const chatCloseTime = meetingTime.clone().add(1, "hours");

//       // Also save to MongoDB for persistence
//       const mongoChat = new Chat({
//         invitationId,
//         participants: participantIds,
//         isGroupChat: isGroupMeeting || false,
//         chatOpenTime: meetingTime,
//         chatCloseTime: chatCloseTime.toDate(),
//         active: true,
//       });

//       await mongoChat.save();
//       console.log("mongoChat", mongoChat);
//       const lastMessage =
//         mongoChat.messages.length > 0
//           ? mongoChat.messages[mongoChat.messages.length - 1]
//           : null;
//       // Set up scheduled functions for chat open/close notifications
//       await this.scheduleNotifications(mongoChat);

//       return { ...mongoChat._doc, lastMessage };
//     } catch (error) {
//       console.error("Error creating chat:", error);
//       throw error;
//     }
//   }

//   /**
//    * Set up the chat time window and schedule notifications
//    * @param {Object} chatData - Chat data
//    * @returns {Promise<void>}
//    */
//   async setupChatTimeWindow(chatData) {
//     try {
//       const { invitationId, participants, chatOpenTime, chatCloseTime } =
//         chatData;

//       // Schedule notifications for chat open
//       const now = new Date();
//       if (chatOpenTime > now) {
//         // Schedule chat open notification
//         await this.scheduleNotification(
//           participants,
//           chatOpenTime,
//           "Your chat is now open for coordination before your meetup."
//         );
//       }

//       // Schedule chat close notification
//       if (chatCloseTime > now) {
//         await this.scheduleNotification(
//           participants,
//           chatCloseTime,
//           "Your chat window has closed. You can still view messages but cannot send new ones."
//         );
//       }

//       return null;
//     } catch (error) {
//       console.error("Error setting up chat time window:", error);
//       return null;
//     }
//   }

//   /**
//    * Schedule notifications for chat events
//    * @param {Object} chatData - Chat data
//    * @returns {Promise<void>}
//    */
//   async scheduleNotifications(chatData) {
//     const { participants, chatOpenTime } = chatData;

//     // Get user details for customized notifications
//     const users = await User.find({ _id: { $in: participants } }).select(
//       "_id isPremium fcmToken"
//     );

//     // Schedule open notifications
//     for (const user of users) {
//       let notificationMessage;

//       if (user.isPremium) {
//         notificationMessage =
//           "Your meetup is in 24 hours – the chat is now open for any last-minute coordination.";
//       } else {
//         notificationMessage =
//           "Your meetup is in 3 hours – the chat is now open for any last-minute coordination.";
//       }

//       if (user.fcmToken) {
//         // Schedule the notification at chat open time
//         await this.scheduleNotification(
//           [user._id],
//           chatOpenTime,
//           notificationMessage
//         );
//       }
//     }
//   }

//   /**
//    * Schedule a notification to be sent at a specific time
//    * @param {Array} userIds - Array of user IDs
//    * @param {Date} scheduledTime - Time to send notification
//    * @param {String} message - Notification message
//    * @returns {Promise<void>}
//    */
//   async scheduleNotification(userIds, scheduledTime, message) {
//     try {
//       // Get FCM tokens for users
//       const users = await User.find({ _id: { $in: userIds } }).select(
//         "fcmToken"
//       );
//       const tokens = users.filter((u) => u.fcmToken).map((u) => u.fcmToken);

//       if (tokens.length === 0) return;

//       // Calculate delay until scheduled time
//       const now = new Date();
//       const delay = scheduledTime.getTime() - now.getTime();

//       if (delay <= 0) {
//         // Send immediately if time has passed
//         await this.sendPushNotification(tokens, message);
//       } else {
//         // Schedule for future
//         setTimeout(async () => {
//           await this.sendPushNotification(tokens, message);
//         }, delay);
//       }
//     } catch (error) {
//       console.error("Error scheduling notification:", error);
//     }
//   }

//   /**
//    * Send push notification to users
//    * @param {Array} tokens - FCM tokens
//    * @param {String} message - Notification message
//    * @returns {Promise<void>}
//    */
//   async sendPushNotification(tokens, message) {
//     try {
//       if (!tokens || tokens.length === 0) return;

//       const notification = {
//         notification: {
//           title: "NETME Chat",
//           body: message,
//         },
//         tokens: tokens,
//       };

//       await messaging.sendMulticast(notification);
//     } catch (error) {
//       console.error("Error sending push notification:", error);
//     }
//   }

//   /**
//    * Validate and process a new message
//    * @param {Object} messageData - Message data
//    * @returns {Promise<Object|null>} - Processed message or null if invalid
//    */
//   async validateAndProcessMessage(messageData) {
//     try {
//       console.log("messageData", messageData);
//       const { chatId, senderId, content, timestamp } = messageData;
//       console.log("Processing message for chat ID:", chatId);

//       // // First try to find the chat by chatId
//       // let chatDoc = await this.chatsCollection.doc(chatId).get();
//       // let firestoreDocId = chatId; // Default to the provided chatId

//       // // If not found, try to find by invitationId or chatId field
//       // if (!chatDoc.exists) {
//       //   console.log(
//       //     "Chat document not found directly, trying alternative lookups"
//       //   );

//       //   // Try to find a chat where the chatId field matches our chatId
//       //   const chatQuery = await this.chatsCollection
//       //     .where("chatId", "==", chatId)
//       //     .get();
//       //   if (!chatQuery.empty) {
//       //     chatDoc = chatQuery.docs[0];
//       //     firestoreDocId = chatDoc.id;
//       //     console.log("Found chat by chatId field:", firestoreDocId);
//       //   } else {
//       //     // Try finding by invitationId
//       //     const invitationQuery = await this.chatsCollection
//       //       .where("invitationId", "==", chatId)
//       //       .get();
//       //     if (!invitationQuery.empty) {
//       //       chatDoc = invitationQuery.docs[0];
//       //       firestoreDocId = chatDoc.id;
//       //       console.log("Found chat by invitationId:", firestoreDocId);
//       //     } else {
//       //       console.log("Chat not found with ID:", chatId);
//       //       return {
//       //         error: "Chat not found. Please initialize the chat first.",
//       //       };
//       //     }
//       //   }
//       // }

//       // const chatData = chatDoc.data();
//       // console.log("Chat data:", chatData);
//       const chatData = await Chat.findOne({ chatId });
//       console.log("Chat data:", chatData);
//       const {
//         participants,
//         chatOpenTime,
//         chatCloseTime,
//         isGroupChat,
//         // hasPremiumUser,
//       } = chatData;

//       // Check if sender is a participant
//       if (!participants.includes(senderId)) {
//         return { error: "User is not a participant in this chat" };
//       }

//       // Get sender's user data
//       const sender = await User.findById(senderId).select("isPremium userName");
//       if (!sender) {
//         return { error: "Sender not found" };
//       }

//       const now = new Date();
//       const isPremiumSender = sender.isPremium;

//       // Check if chat is within time window
//       if (chatOpenTime && now < new Date(chatOpenTime.seconds * 1000)) {
//         // Chat not open yet
//         if (isPremiumSender) {
//           // Premium users can send messages early
//           if (!chatData.premiumEarlyStart) {
//             await Chat.updateOne({ chatId }, { premiumEarlyStart: true });
//           }
//         } else {
//           // Standard user trying to send message before chat is open
//           if (!chatData.premiumEarlyStart) {
//             // No premium user has started the chat early
//             return {
//               error: "Chat not open yet",
//               openTime: chatOpenTime,
//               isPremium: isPremiumSender,
//             };
//           } else {
//             // A premium user has started the chat early
//             // Check if standard user has already sent 2 messages
//             const userMessages = await Chat.find({
//               chatId,
//               senderId,
//             });

//             if (userMessages.length >= 2) {
//               return {
//                 error:
//                   "Message limit reached. Upgrade to Premium for unlimited messages.",
//                 isPremium: isPremiumSender,
//               };
//             }
//           }
//         }
//       } else if (chatCloseTime && now > new Date(chatCloseTime)) {
//         // Chat closed
//         return {
//           error: "Chat is closed",
//           closeTime: chatCloseTime,
//         };
//       }

//       // If it's a group chat, check if it has at least two participants who accepted
//       if (isGroupChat) {
//         // For group chats, we need to check if at least two people have accepted
//         const meeting = await Invitation.findById(chatData.invitationId);
//         if (!meeting) {
//           return { error: "Meeting not found" };
//         }

//         const acceptedUsers = meeting.users.filter(
//           (p) => p.status === "Accepted"
//         );
//         if (acceptedUsers.length < 2) {
//           return {
//             error: "Group chat requires at least two accepted participants",
//             acceptedCount: acceptedUsers.length,
//           };
//         }
//       }

//       // Message is valid, create it in the format matching your existing messages
//       // Create a unique ID for the chat room if needed
//       let chatRoomId;
//       if (isGroupChat) {
//         chatRoomId = chatData.chatId;
//       } else {
//         // For 1-on-1 chats, create a room ID from the two participant IDs
//         const sortedParticipants = [...participants].sort();
//         chatRoomId = sortedParticipants.join("_");
//       }

//       console.log("Using chat room ID:", chatRoomId);

//       // Convert timestamp to number format (microseconds since epoch)
//       const timestampMicros = (timestamp || now).getTime() * 1000;

//       // Create read status object with participant IDs as keys
//       const readStatus = {};
//       participants.forEach((p) => {
//         readStatus[p] = p === senderId; // true for sender, false for others
//       });

//       // Create unread count object
//       const numberOfUnread = {};
//       participants.forEach((p) => {
//         numberOfUnread[p] = p === senderId ? 0 : 1; // 0 for sender, 1 for others
//       });

//       // Define constants for chat activation windows
//       const STANDARD_OPEN_HOURS_BEFORE = 3;
//       const PREMIUM_OPEN_HOURS_BEFORE = 24;
//       const CLOSE_HOURS_AFTER = 1;

//       // These constants should match the UI display values
//       const STATUS_OPEN = "Open now";
//       const STATUS_OPENS_SOON = "Opens soon";
//       const STATUS_CLOSED = "Closed";

//       const COLOR_OPEN = "39FF14"; // Neon green
//       const COLOR_OPENS_SOON = "808080"; // Gray
//       const COLOR_CLOSED = "FF0000"; // Red

//       // Create the message in individual_chatrooms collection
//       const messageRef = await Chat.findOneAndUpdate(
//         { chatId },
//         {
//           $push: {
//             messages: {
//               _id: messageId,
//               chat: content,
//               fromId: senderId,
//               isAudio: isAudio,
//               audioUrl: audioUrl, // Add audio URL for audio messages
//               isInfo: isInfo,
//               timestamp: timestampMicros,
//               readStatus,
//               numberOfUnread,
//             },
//           },
//         }
//       );
//       const messageId = messageRef._id;

//       // Extract additional properties from messageData
//       const { isAudio = false, audioUrl = null, isInfo = false } = messageData;

//       const message = {
//         _id: messageId,
//         chat: content,
//         fromId: senderId,
//         isAudio: isAudio,
//         audioUrl: audioUrl, // Add audio URL for audio messages
//         isInfo: isInfo,
//         isNewUser: false,
//         timestamp: timestampMicros,
//         read: readStatus,
//         numberOfUnread: numberOfUnread,
//         chatId: firestoreDocId,
//         users: participants,
//       };

//       console.log("Saving message:", message);
//       await messageRef.set(message);

//       // Update chat room document
//       const chatRoom = await Chat.findOne({ chatId });

//       // Prepare chat room data
//       const chatRoomUpdateData = {
//         chat: content,
//         fromId: senderId,
//         timestamp: timestampMicros,
//         typingBy: "",
//         updatedAt: new Date().toISOString(),
//         read: readStatus,
//         numberOfUnread: numberOfUnread,
//         isAudio: isAudio,
//         isInfo: isInfo,
//       };

//       // Add audioUrl if it's an audio message
//       if (isAudio && audioUrl) {
//         chatRoomUpdateData.audioUrl = audioUrl;
//       }

//       if (chatRoom) {
//         // Update existing chat room
//         await chatRoom.updateOne(chatRoomUpdateData);
//       } else {
//         // Create new chat room
//         await Chat.create({
//           ...chatRoomUpdateData,
//           chatId: chatRoomId,
//           invitationId: chatData.invitationId,
//           isNewUser: false,
//           isTyping: false,
//           users: participants,
//         });
//       }

//       // Also save to MongoDB for persistence
//       await Chat.findOneAndUpdate(
//         { invitationId: chatData.invitationId },
//         {
//           $push: {
//             messages: {
//               sender: senderId,
//               content,
//               timestamp: now,
//               read: participants.map((p) => ({
//                 user: p,
//                 readAt: p === senderId ? now : null,
//               })),
//             },
//           },
//           updatedAt: now,
//         }
//       );

//       // Send push notification to other participants
//       const otherParticipants = participants.filter((p) => p !== senderId);
//       if (otherParticipants.length > 0) {
//         const users = await User.find({
//           _id: { $in: otherParticipants },
//         }).select("fcmToken");
//         const tokens = users.filter((u) => u.fcmToken).map((u) => u.fcmToken);

//         if (tokens.length > 0) {
//           const notificationMessage = `${
//             sender.name || "Someone"
//           } sent you a message`;
//           await this.sendPushNotification(tokens, notificationMessage);
//         }
//       }
//       console.log("message", message);
//       return message;
//     } catch (error) {
//       console.error("Error validating message:", error);
//       return { error: "Internal server error: " + error.message };
//     }
//   }

//   /**
//    * Get chat status for UI display
//    * @param {String} chatId - Chat ID
//    * @param {String} userId - User ID
//    * @returns {Promise<Object>} - Chat status
//    */
//   async getChatStatus(chatId, userId) {
//     try {
//       const chatData = await Chat.findById(chatId);
//       if (!chatData) {
//         return { error: "Chat not found" };
//       }
//       console.log("chatData", chatData);
//       const { chatOpenTime, chatCloseTime } = chatData;
//       console.log("chatData", chatData);
//       // const now = new Date();

//       // Get user data to check if premium
//       const user = await User.findById(userId).select("isPremium");
//       if (!user) {
//         return { error: "User not found" };
//       }

//       let status = "";
//       let color = "";
//       let message = "";
//       // Define constants for chat activation windows
//       const STANDARD_OPEN_HOURS_BEFORE = 3;
//       const PREMIUM_OPEN_HOURS_BEFORE = 24;
//       const CLOSE_HOURS_AFTER = 1;

//       // These constants should match the UI display values
//       const STATUS_OPEN = "Open now";
//       const STATUS_OPENS_SOON = "Opens soon";
//       const STATUS_CLOSED = "Closed";

//       const COLOR_OPEN = "39FF14"; // Neon green
//       const COLOR_OPENS_SOON = "808080"; // Gray
//       const COLOR_CLOSED = "FF0000"; // Red
//       console.log("chatOpenTime", chatOpenTime);
//       console.log("chatCloseTime", chatCloseTime);
//       const seconds = Math.floor(Date.now() / 1000);
//       console.log("seconds getChatStatus", seconds);
//       if (seconds < chatOpenTime) {
//         console.log("Chat not open yet", seconds < chatOpenTime);
//         // Chat not open yet
//         status = STATUS_OPENS_SOON;
//         color = COLOR_OPENS_SOON; // Grey color

//         if (user.isPremium) {
//           // message =
//           // "The chat will open automatically 24 hours before your meetup.";
//           // Chat open
//           status = STATUS_OPEN;
//           color = COLOR_OPEN;
//           message = "Chat is open for coordination.";
//         } else {
//           message =
//             "The chat will open automatically 3 hours before your meetup.";

//           // Calculate hours until chat opens
//           const hoursUntilOpen = Math.ceil(
//             (chatOpenTime - seconds) / (1000 * 60 * 60)
//           );
//           console.log("hoursUntilOpen", hoursUntilOpen);
//           if (chatData.premiumEarlyStart) {
//             if (chatData.messages.length >= 2) {
//               message = `The chat will open in ${hoursUntilOpen} hours – want to send a message already?`;
//             }
//           }
//         }
//       } else if (seconds > chatCloseTime) {
//         console.log("Chat closed", seconds > chatCloseTime);
//         // Chat closed
//         status = STATUS_CLOSED;
//         color = COLOR_CLOSED; // Red color
//         message = "This chat is now closed.";
//       } else {
//         // Chat open
//         status = STATUS_OPEN;
//         color = COLOR_OPEN;
//         message = "Chat is open for coordination.";
//       }

//       return {
//         status,
//         color,
//         message,
//         chatOpenTime,
//         chatCloseTime,
//         isPremium: user.isPremium,
//         canSendMessage: seconds >= chatOpenTime && seconds <= chatCloseTime,
//       };
//     } catch (error) {
//       console.error("Error getting chat status:", error);
//       return { error: "Internal server error" };
//     }
//   }

//   async getStatus(chatData, userId) {
//     let status = "";
//     let color = "";
//     let message = "";
//     const openTimestamp = Math.floor(
//       new Date(chatData.chatOpenTime).getTime() / 1000
//     );
//     const closeTimestamp = Math.floor(
//       new Date(chatData.chatCloseTime).getTime() / 1000
//     );
//     console.log("chatOpenTime", openTimestamp);
//     console.log("chatCloseTime", closeTimestamp);
//     const seconds = Math.floor(Date.now() / 1000);
//     console.log("seconds getChatStatus", seconds);
//     const user = await User.findById(userId).select("isPremium");
//     if (seconds < openTimestamp) {
//       console.log("Chat not open yet", seconds < openTimestamp);
//       // console.log("chatData", chatData);
//       // Chat not open yet
//       status = STATUS_OPENS_SOON;
//       color = COLOR_OPENS_SOON; // Grey color

//       if (user?.isPremium) {
//         message =
//           "The chat will open automatically 24 hours before your meetup.";
//       } else {
//         message =
//           "The chat will open automatically 3 hours before your meetup.";

//         // Calculate hours until chat opens
//         const hoursUntilOpen = Math.ceil(
//           (openTimestamp - seconds) / (1000 * 60 * 60)
//         );
//         console.log("hoursUntilOpen", hoursUntilOpen);
//         if (chatData.premiumEarlyStart) {
//           // Count user's messages
//           const userMessages = await Chat.find({
//             chatId: chatData._id,
//             messages: {
//               sender: userId,
//             },
//           });

//           if (userMessages.length >= 2) {
//             message = `The chat will open in ${hoursUntilOpen} hours – want to send a message already?`;
//           }
//         }
//       }
//     } else if (seconds > closeTimestamp) {
//       console.log("Chat closed", seconds > closeTimestamp);
//       // Chat closed
//       status = "Closed";
//       color = "FF0000"; // Red color
//       message = "This chat is now closed.";
//     } else {
//       // Chat open
//       status = "Open now";
//       color = "39FF14";
//       message = "Chat is open for coordination.";
//     }
//     return {
//       status,
//       color,
//       message,
//       chatOpenTime: openTimestamp,
//       chatCloseTime: closeTimestamp,
//       isPremium: user.isPremium,
//       canSendMessage: seconds >= openTimestamp && seconds <= closeTimestamp,
//     };
//   }

//   /**
//    * Mark messages as read
//    * @param {String} chatId - Chat ID
//    * @param {String} userId - User ID
//    * @returns {Promise<Object>} - Result
//    */
//   /**
//    * Send an automatic info message
//    * @param {String} chatId - Chat ID
//    * @param {String} senderId - Sender user ID
//    * @param {String} message - Info message content
//    * @returns {Promise<Object>} - Result
//    */
//   async sendInfoMessage(chatId, senderId, message) {
//     try {
//       const messageData = {
//         chatId,
//         senderId,
//         content: message,
//         timestamp: new Date(),
//         isAudio: false,
//         audioUrl: null,
//         isInfo: true,
//       };

//       return await this.validateAndProcessMessage(messageData);
//     } catch (error) {
//       console.error("Error sending info message:", error);
//       return { error: "Failed to send info message: " + error.message };
//     }
//   }

//   /**
//    * Mark messages as read
//    * @param {String} chatId - Chat ID
//    * @param {String} userId - User ID
//    * @returns {Promise<Object>} - Result
//    */
//   async markMessagesAsRead(chatId, userId) {
//     try {
//       const now = new Date();
//       const seconds = Math.floor(now.getTime() / 1000);
//       const microSeconds = now.getTime() * 1000; // Convert to microseconds for Firebase
//       let messagesUpdated = 0;
//       let chatDoc;

//       // First try to find the chat by chatId
//       try {
//         chatDoc = await this.chatsCollection.doc(chatId).get();

//         // If not found, try to find by chatId field
//         if (!chatDoc.exists) {
//           const chatQuery = await this.chatsCollection
//             .where("chatId", "==", chatId)
//             .get();
//           if (!chatQuery.empty) {
//             chatDoc = chatQuery.docs[0];
//           } else {
//             // Try one more approach - maybe the chatId is actually the invitationId
//             const invitationQuery = await this.chatsCollection
//               .where("invitationId", "==", chatId)
//               .get();
//             if (!invitationQuery.empty) {
//               chatDoc = invitationQuery.docs[0];
//             }
//           }
//         }
//       } catch (error) {
//         console.error("Error finding chat document:", error);
//       }

//       // Process based on chat type (group or individual)
//       if (chatDoc && chatDoc.exists) {
//         const chatData = chatDoc.data();
//         const isGroupChat = chatData.isGroupChat;

//         if (isGroupChat) {
//           // For group chats, update messages in the messages collection
//           let messagesSnapshot;
//           try {
//             messagesSnapshot = await this.messagesCollection
//               .where("chatId", "==", chatDoc.id)
//               .get();
//           } catch (indexError) {
//             console.warn(
//               "Firestore index error in markMessagesAsRead:",
//               indexError.message
//             );
//             console.warn(
//               "Please create the required index using the link in the error message above."
//             );
//             // Return empty snapshot to continue execution
//             messagesSnapshot = { empty: true, docs: [] };
//           }

//           if (!messagesSnapshot.empty) {
//             const batch = db.batch();
//             messagesSnapshot.forEach((doc) => {
//               const messageData = doc.data();

//               // Update read status
//               if (messageData.read && messageData.read[userId] === false) {
//                 const readUpdate = { ...messageData.read };
//                 readUpdate[userId] = true;

//                 // Update unread count
//                 const unreadUpdate = { ...messageData.numberOfUnread };
//                 unreadUpdate[userId] = 0;

//                 batch.update(doc.ref, {
//                   read: readUpdate,
//                   numberOfUnread: unreadUpdate,
//                 });
//                 messagesUpdated++;
//               }
//             });

//             await batch.commit();
//           }
//         } else {
//           // For individual chats, update the chat room document
//           // Create the chat room ID from participant IDs
//           const participants = chatData.participants;
//           const sortedParticipants = [...participants].sort();
//           const chatRoomId = sortedParticipants.join("_");

//           const chatRoomRef = db
//             .collection("individual_chatrooms")
//             .doc(chatRoomId);
//           const chatRoomDoc = await chatRoomRef.get();

//           if (chatRoomDoc.exists) {
//             const chatRoomData = chatRoomDoc.data();

//             // Update read status
//             const readUpdate = { ...chatRoomData.read };
//             readUpdate[userId] = true;

//             // Update unread count
//             const unreadUpdate = { ...chatRoomData.numberOfUnread };
//             unreadUpdate[userId] = 0;

//             await chatRoomRef.update({
//               read: readUpdate,
//               numberOfUnread: unreadUpdate,
//               lastReadAt: microSeconds,
//             });

//             messagesUpdated = 1; // At least one update
//           }
//         }
//       }

//       // Also update in MongoDB
//       const mongoUpdate = await Chat.findOneAndUpdate(
//         {
//           invitationId: chatId,
//           "messages.read.user": userId,
//           "messages.read.readAt": null,
//         },
//         {
//           $set: { "messages.$[elem].read.$[reader].readAt": now },
//         },
//         {
//           arrayFilters: [
//             { "elem.read.user": userId, "elem.read.readAt": null },
//             { "reader.user": userId },
//           ],
//           multi: true,
//         }
//       );

//       return {
//         success: true,
//         count: messagesUpdated,
//         message: "Messages marked as read successfully",
//       };
//     } catch (error) {
//       console.error("Error marking messages as read:", error);
//       return { error: "Internal server error: " + error.message };
//     }
//   }

//   /**
//    * Get chat data for re-invite flow
//    * @param {String} chatId - Chat ID
//    * @returns {Promise<Object>} - Chat data for re-invite
//    */
//   async getChatForReInvite(chatId) {
//     try {
//       const chatDoc = await this.chatsCollection.doc(chatId).get();
//       if (!chatDoc.exists) {
//         return { error: "Chat not found" };
//       }

//       const chatData = chatDoc.data();
//       const { participants, isGroupChat } = chatData;

//       // Get participant details
//       const users = await User.find({ _id: { $in: participants } }).select(
//         "_id userName coverImage location"
//       );

//       return {
//         chatId,
//         participants: users,
//         isGroupChat,
//       };
//     } catch (error) {
//       console.error("Error getting chat for re-invite:", error);
//       return { error: "Internal server error" };
//     }
//   }

//   /**
//    * Count messages sent by a user in a chat
//    * @param {String} chatId - Chat ID
//    * @param {String} userId - User ID
//    * @returns {Promise<Number>} - Message count
//    */
//   async countUserMessages(chatId, userId) {
//     try {
//       console.log(`Counting messages for user ${userId} in chat ${chatId}`);

//       // Get chat data
//       const chat = await Chat.findById(chatId).populate("invitationId");

//       if (!chat) {
//         console.error(`Chat not found: ${chatId}`);
//         return 0;
//       }

//       let messageCount = 0;

//       // For group chats
//       if (chat.isGroupChat) {
//         try {
//           // Query messages in Firebase where sender is the current user and not info messages
//           const messagesSnapshot = await this.messagesCollection
//             .where("chatId", "==", chatId)
//             .where("senderId", "==", userId)
//             .where("isInfo", "==", false)
//             .get();

//           messageCount = messagesSnapshot.size;
//           console.log(`Found ${messageCount} messages in group chat`);
//         } catch (error) {
//           console.warn("Error querying group chat messages:", error);
//           // Fall back to MongoDB
//           if (chat.messages && Array.isArray(chat.messages)) {
//             messageCount = chat.messages.filter(
//               (msg) => msg.sender.toString() === userId && !msg.isInfo
//             ).length;
//           }
//         }
//       }
//       // For individual chats
//       else {
//         try {
//           // Create the chat room ID from participant IDs
//           const participants = chat.participants.map((p) => p.toString());
//           const sortedParticipants = [...participants].sort();
//           const chatRoomId = sortedParticipants.join("_");

//           // Get the chat room document
//           const chatRoomRef = db
//             .collection("individual_chatrooms")
//             .doc(chatRoomId);
//           const chatRoomDoc = await chatRoomRef.get();

//           if (chatRoomDoc.exists) {
//             const chatRoomData = chatRoomDoc.data();

//             // Count messages where sender is the current user and not info messages
//             if (chatRoomData.messages && Array.isArray(chatRoomData.messages)) {
//               messageCount = chatRoomData.messages.filter(
//                 (msg) => msg.senderId === userId && !msg.isInfo
//               ).length;
//             }

//             console.log(`Found ${messageCount} messages in individual chat`);
//           }
//         } catch (error) {
//           console.warn("Error querying individual chat messages:", error);
//           // Fall back to MongoDB
//           if (chat.messages && Array.isArray(chat.messages)) {
//             messageCount = chat.messages.filter(
//               (msg) => msg.sender.toString() === userId && !msg.isInfo
//             ).length;
//           }
//         }
//       }

//       return messageCount;
//     } catch (error) {
//       console.error("Error counting user messages:", error);
//       return 0;
//     }
//   }
// }

// module.exports = new FirebaseChatService();
