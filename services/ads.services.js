const { PutObjectCommand, S3Client } = require("@aws-sdk/client-s3");
const { SESClient, SendEmailCommand } = require("@aws-sdk/client-ses");
const Ad = require("../models/Become/Ads");

const s3 = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.SES_PASS,
    secretAccessKey: process.env.SES_USER,
  },
});

const sesClient = new SESClient({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.SES_PASS,
    secretAccessKey: process.env.SES_USER,
  },
});

exports.getAdsService = async () => {
  const ads = await Ad.find({});
  return ads;
};

exports.createAdsService = async (body) => {
  //console.log("body", body);
  const ads = await Ad.create(body);
  return ads;
};

exports.uploadFileService = async (file) => {
  const fileName = file.originalname.replaceAll(" ", "_");
  const currentDate = new Date().toISOString().split("T")[0].replace(/-/g, "");
  const Body = file.buffer;
  const Bucket = "netme-stage";
  const url = `https://${Bucket}.s3.us-east-1.amazonaws.com/${currentDate}/${fileName}`;

  await s3.send(
    new PutObjectCommand({
      Bucket,
      Key: `${currentDate}/${fileName}`,
      Body,
      ContentType: file.mimetype,
    })
  );

  return url;
};

exports.sendEmailService = async () => {
  const params = {
    Source: "<EMAIL>",
    Destination: {
      ToAddresses: ["<EMAIL>"],
    },
    Message: {
      Subject: {
        Data: "Test Email from AWS SDK v3",
      },
      Body: {
        Text: {
          Data: "Hello, this is a test email sent using AWS SDK v3 for SES.",
        },
      },
    },
  };

  try {
    const data = await sesClient.send(new SendEmailCommand(params));
    console.log("Email sent successfully:", data);
  } catch (err) {
    console.error("Failed to send email:", err);
  }
};
