const bcrypt = require("bcryptjs");
const Partner = require("../models/Partner/Partner");
exports.getPartnerService = async () => {
  const data = await Partner.find({});
  return data;
};

exports.createPartnerService = async (body) => {
  // console.log("body", body);
  // Hash the password before creating the partner
  const saltRounds = 10; // Adjust saltRounds as needed (higher for stronger hashing)
  const hashedPassword = await bcrypt.hash(body.password, saltRounds);
  body.password = hashedPassword;
  const data = await Partner.create(body);
  // console.log("data", data);
  return data;
};
exports.deletePartnerByIdService = async (id = "") => {
  const data = await Partner.deleteOne({ _id: id });
  return data;
};
