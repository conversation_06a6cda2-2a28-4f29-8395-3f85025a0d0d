const { PutObjectCommand, S3Client } = require("@aws-sdk/client-s3");
const { SESClient, SendEmailCommand } = require("@aws-sdk/client-ses");
const sharp = require("sharp");

const bcrypt = require("bcryptjs");
const Business = require("../models/Partner/partnerBusiness");
const SubscriptionModel = require("../models/Admin/subscription");

const Partner = require("../models/Partner/Partner");
const Stripe = require("stripe");
const PartnerSubscription = require("../models/Partner/partnerSubscription");

exports.stripe = new Stripe(process.env.STRIPE_SECRET, {
  apiVersion: "2024-06-20",
  typescript: true,
});

const s3 = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.ACCESS_KEY_ID,
    secretAccessKey: process.env.SECRET_ACCESS_KEY,
  },
});

const sesClient = new SESClient({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID_SES,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY_SES,
  },
});

exports.getUserService = async () => {
  const leads = await Business.find({}).populate("partnerId");
  return leads;
};

exports.createUserService = async (body) => {
  const {
    isPremium = false,
    isWebsiteNotification = false,
    isEmailNotification = false,
    password = "12345678",
    active = false,
    isNotificationsEnabled = true,
    status = "Requested",
    userType = "PARTNER",
    deleted = false,
  } = body.legalDetails;
  const hashedPassword = await bcrypt.hash(password, 10);
  const partner = await Partner.create({
    ...body.legalDetails,
    isPremium,
    isWebsiteNotification,
    isEmailNotification,
    password: hashedPassword,
    active,
    isNotificationsEnabled,
    status,
    userType,
    deleted,
  });

  const business = await Business.create({
    partnerId: partner.id,
    ...body.businessDetails,
  });

  const sessionId = body.sessionId;

  if (!sessionId) {
    return res.status(400).json({ error: "Missing sessionId" });
  }

  const session = await this.stripe.checkout.sessions.retrieve(sessionId);

  if (!session) {
    return res.status(404).json({ error: "Session not found" });
  }

  const stripeSubscriptionId = session.subscription;
  if (!stripeSubscriptionId) {
    return res
      .status(400)
      .json({ error: "Subscription ID not found in session" });
  }

  const subscription = await this.stripe.subscriptions.retrieve(
    stripeSubscriptionId
  );

  const invoice = await this.stripe.invoices.retrieve(
    subscription.latest_invoice
  );

  console.log("invoice...........=> ", invoice);

  const paymentIntentId = invoice.payment_intent;
  console.log("paymentIntentId...........=> ", paymentIntentId);
  const expireAt = new Date(subscription.current_period_end * 1000);

  const metadata = session.metadata;
  const { subscriptionId: selectedId, usedVoucher: voucher } = metadata;
  // mr

  const findSubscription = await SubscriptionModel.findOne({
    _id: selectedId,
    userType: "PARTNER",
    status: true,
  });

  console.log(
    "findSubscription?.timePeriod...........=> ",
    findSubscription?.timePeriod
  );
  const trialEndDate = Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60;
  await this.stripe.subscriptions.update(stripeSubscriptionId, {
    trial_end: trialEndDate,
  });
  await PartnerSubscription.create({
    partnerId: partner?._id?.toString(),
    expireAt,
    usedVoucher: voucher,
    isActive: false,
    timePeriod: Number(findSubscription?.timePeriod),
    stripeSubscriptionId,
    stripePaymentId: paymentIntentId,
    subscriptionId: selectedId,
  });

  return partner;
};

exports.uploadFileService = async (file) => {
  console.log("I called upload service");
  const fileName = file.originalname.replaceAll(" ", "_");
  const currentDate = new Date().toISOString().split("T")[0].replace(/-/g, "");
  // const Body = file.buffer;
  const Bucket = process.env.S3_BUCKET_NAME;
  const url = `https://${Bucket}.s3.eu-west-2.amazonaws.com/${currentDate}/${fileName}`;
  // Use Sharp to resize/compress the image before uploading
  const optimizedImage = await sharp(file.buffer)
    .resize(1024) // Resize the image (e.g., width of 1024px)
    .jpeg({ quality: 80 }) // Convert to JPEG and set quality (adjust as needed)
    .toBuffer(); // Convert the image back to a buffer for S3 upload
  await s3.send(
    new PutObjectCommand({
      Bucket,
      Key: `${currentDate}/${fileName}`,
      Body: optimizedImage,
      ContentType: file.mimetype,
    })
  );

  return url;
};

exports.sendEmailService = async (body = {}) => {
  const {
    subject = "New Ad Request",
    email = "<EMAIL>",
    data = "test message",
  } = body;
  const params = {
    Source: "<EMAIL>",
    Destination: {
      ToAddresses: ["<EMAIL>"],
    },
    Message: {
      Subject: {
        Data: subject,
      },
      Body: {
        Html: {
          Data: `<table style="width: 100%; border-collapse: collapse;">
        <tr style="background-color: #f8f8f8;">
            <th style="border: 1px solid #dddddd; padding: 8px; text-align: left;">Field</th>
            <th style="border: 1px solid #dddddd; padding: 8px; text-align: left;">Value</th>
        </tr>
        <tr>
            <td style="border: 1px solid #dddddd; padding: 8px;">Title</td>
            <td style="border: 1px solid #dddddd; padding: 8px;"> ${body.title} </td>
        </tr>
        <tr style="background-color: #f8f8f8;">
            <td style="border: 1px solid #dddddd; padding: 8px;">Body</td>
            <td style="border: 1px solid #dddddd; padding: 8px;"> ${body.body} </td>
        </tr>
        <tr>
            <td style="border: 1px solid #dddddd; padding: 8px;">Legal Representative</td>
            <td style="border: 1px solid #dddddd; padding: 8px;">
                Full Name: ${body.legalRepresentative.fullName} <br>
                // Last Name: ${body.legalRepresentative.lastName}<br>
                Email: ${body.legalRepresentative.email}<br>
                Mobile: ${body.legalRepresentative.mobile}
            </td>
        </tr>
        <tr style="background-color: #f8f8f8;">
            <td style="border: 1px solid #dddddd; padding: 8px;">Company Name</td>
            <td style="border: 1px solid #dddddd; padding: 8px;">${body.companyName}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #dddddd; padding: 8px;">Company Tax Number</td>
            <td style="border: 1px solid #dddddd; padding: 8px;">${body.companyTaxNumber}</td>
        </tr>
        <tr style="background-color: #f8f8f8;">
            <td style="border: 1px solid #dddddd; padding: 8px;">Address</td>
            <td style="border: 1px solid #dddddd; padding: 8px;">${body.address}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #dddddd; padding: 8px;">Business Email</td>
            <td style="border: 1px solid #dddddd; padding: 8px;">${body.businessEmail}</td>
        </tr>
        <tr style="background-color: #f8f8f8;">
            <td style="border: 1px solid #dddddd; padding: 8px;">Business Mobile</td>
            <td style="border: 1px solid #dddddd; padding: 8px;"> ${body.businessMobile} </td>
        </tr>
        <tr>
            <td style="border: 1px solid #dddddd; padding: 8px;">Category</td>
            <td style="border: 1px solid #dddddd; padding: 8px;"> ${body.category} </td>
        </tr>
        <tr style="background-color: #f8f8f8;">
            <td style="border: 1px solid #dddddd; padding: 8px;">City</td>
            <td style="border: 1px solid #dddddd; padding: 8px;">${body.city}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #dddddd; padding: 8px;">Target Group From</td>
            <td style="border: 1px solid #dddddd; padding: 8px;"> ${body.targerGroupFrom} </td>
        </tr>
        <tr style="background-color: #f8f8f8;">
            <td style="border: 1px solid #dddddd; padding: 8px;">Target Group To</td>
            <td style="border: 1px solid #dddddd; padding: 8px;"> ${body.targetGroupTo} </td>
        </tr>
        <tr>
            <td style="border: 1px solid #dddddd; padding: 8px;">Release Date</td>
            <td style="border: 1px solid #dddddd; padding: 8px;"> ${body.releaseDate} </td>
        </tr>
        <tr style="background-color: #f8f8f8;">
            <td style="border: 1px solid #dddddd; padding: 8px;">Ad Type</td>
            <td style="border: 1px solid #dddddd; padding: 8px;"> ${body.adType} </td>
        </tr>
    </table>`,
        },
      },
    },
  };

  const params2 = {
    Source: "<EMAIL>",
    Destination: {
      ToAddresses: [body.legalRepresentative.email],
    },
    Message: {
      Subject: {
        Data: "Welcome to NETME - Thank you for your inquiry",
      },
      Body: {
        Html: {
          Data: `<div style="font-family: Arial, sans-serif; color: #333; background-color: #8CC9E9; padding: 20px; max-width: 600px; margin: 20px auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <div style="background-color: #007BFF; color: white; padding: 10px 15px; border-radius: 8px 8px 0 0; text-align: center;">
                <h1 style="margin: 0; font-size: 24px;">Thank you for your Ads Inquiry!</h1>
            </div>
            <div style="padding: 20px; background-color: white; border-radius: 0 0 8px 8px;">
                <p>Dear ${body.legalRepresentative.firstName} ${body.legalRepresentative.lastName} ,</p>
                <p>Thank you for choosing NETME as your preferred platform for your advertising needs. We're thrilled to embark on this journey with you and are committed to ensuring a successful partnership.</p>
                <p>We have received your ad placement request and are currently reviewing it. Rest assured, our team is diligently working on it to ensure everything aligns with your expectations.</p>
                <p>We will be in touch with you shortly to discuss the details further. In the meantime, if you have any questions or additional requirements, feel free to reach out to us at <a href="mailto:<EMAIL>" style="color: #007BFF; text-decoration: none;"><EMAIL></a>.</p>
                <p>Once again, thank you for choosing NETME. We're excited about the opportunity to work together and look forward to achieving great results with you.</p>
                <p>Best regards,</p>
                <p>The NETME Team</p>
            </div>
        </div>`,
        },
      },
    },
  };
  try {
    const data = await sesClient.send(new SendEmailCommand(params));
    const data2 = await sesClient.send(new SendEmailCommand(params2));
    console.log("Email sent successfully:", data);
    return "";
  } catch (err) {
    console.error("Failed to send email:", err);
    return "";
  }
};
exports.sendGuestListEmail = async (body = {}) => {
  console.log("body", body);
  const { subject, email, sendEmail, firstName, lastName } = body;
  const params = {
    Source: "<EMAIL>",
    Destination: {
      ToAddresses: ["<EMAIL>"],
    },
    Message: {
      Subject: {
        Data: subject,
      },
      Body: {
        Html: {
          Data: `<table style="width: 100%; border-collapse: collapse;">
        <tr style="background-color: #f8f8f8;">
            <th style="border: 1px solid #dddddd; padding: 8px; text-align: left;">Field</th>
            <th style="border: 1px solid #dddddd; padding: 8px; text-align: left;">Value</th>
        </tr>
        <tr style="background-color: #f8f8f8;">
            <td style="border: 1px solid #dddddd; padding: 8px;">First Name</td>
            <td style="border: 1px solid #dddddd; padding: 8px;"> ${firstName} </td>
        </tr>
        <tr style="background-color: #f8f8f8;">
            <td style="border: 1px solid #dddddd; padding: 8px;">Last Name</td>
            <td style="border: 1px solid #dddddd; padding: 8px;"> ${lastName} </td>
        </tr>
        <tr style="background-color: #f8f8f8;">
            <td style="border: 1px solid #dddddd; padding: 8px;">Email</td>
            <td style="border: 1px solid #dddddd; padding: 8px;"> ${email} </td>
        </tr>
    </table>`,
        },
      },
    },
  };

  try {
    const data = await sesClient.send(new SendEmailCommand(params));
    console.log("data", data);
    // const data2 = await sesClient.send(new SendEmailCommand(params2));
    console.log("Email sent successfully:");
    return "";
  } catch (err) {
    console.error("Failed to send email:", err);
    return "";
  }
};
