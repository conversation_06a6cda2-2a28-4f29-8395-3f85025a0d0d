const mongoose = require("mongoose");
const mongoose_delete = require("mongoose-delete");
const { isEmail } = require("validator");

const newsletterSchema = new mongoose.Schema(
  {
    email: {
      type: String,
      lowercase: true,
      index: true,
      validate: [isEmail, "Please enter a valid email"],
    },
  },
  {
    timestamps: true,
  }
);

newsletterSchema.plugin(mongoose_delete, {
  overrideMethods: ["find", "findOne", "update"],
});
const Newsletter = mongoose.model("newsletter", newsletterSchema);

module.exports = Newsletter;
