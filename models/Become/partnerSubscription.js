const mongoose = require("mongoose");
const mongoose_delete = require("mongoose-delete");

const Schema = new mongoose.Schema(
  {
    partnerId: { type: mongoose.Schema.Types.ObjectId, ref: "partner" },
    expireAt: {
      type: Date,
    },
    canceledAt: {
      type: Date,
      default: null,
    },
    endAt: {
      type: Date,
      default: null,
    },
    startAt: {
      type: Date,
      default: null,
    },
    usedVoucher: {
      type: String,
      default: null,
    },
    subscriptionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Subscription",
    },
    stripeSubscriptionId: {
      type: String,
      unique: true,
    },
    stripePaymentId: {
      type: String,
      unique: false,
    },
    timePeriod: {
      type: Number,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isCancel: {
      type: Boolean,
      default: false,
    },
    cancelText: {
      type: String,
    },
  },
  { timestamps: true }
);

Schema.plugin(mongoose_delete, {
  overrideMethods: ["find", "findOne", "findOneAndUpdate", "update"],
});
const PartnerSubscription = mongoose.model("partnerSubscription", Schema);

module.exports = PartnerSubscription;
