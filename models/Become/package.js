const mongoose = require("mongoose");

const subscriptionSchema = new mongoose.Schema(
  {
    planName: {
      type: String,
      index: true,
    },
    timePeriod: {
      type: Number,
    },
    price: {
      type: Number,
    },
    features: {
      type: String,
    },

    status: {
      type: Boolean,
      default: true,
    },

    userType: {
      type: String,
      required: true,
      enum: ["PARTNER", "USER"],
      default: "USER",
    },
  },
  {
    timestamps: true,
  }
);

const Package = mongoose.model("Subscription", subscriptionSchema);

module.exports = Package;
