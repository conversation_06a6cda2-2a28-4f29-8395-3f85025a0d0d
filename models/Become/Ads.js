const mongoose = require("mongoose");

const adSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Please enter a title"],
      trim: true,
    },
    body: {
      type: String,
      required: [true, "Please enter body content"],
      trim: true,
    },
    legalRepresentative: {
      firstName: { type: String, required: true },
      lastName: { type: String, required: true },
      email: { type: String, required: true },
      mobile: { type: String, required: true },
    },
    companyName: { type: String, required: true },
    companyTaxNumber: { type: String, required: true },
    address: { type: String, required: true },
    businessEmail: { type: String, required: true },
    businessMobile: { type: String, required: true },
    category: { type: String, required: true },
    location: {
      type: {
        type: String,
        enum: ["Point"],
      },
      coordinates: {
        type: [Number],
        index: "2dsphere",
      },
    },
    city: { type: String, required: true },
    targerGroupFrom: { type: String, required: true },
    targetGroupTo: { type: String, required: true },
    releaseDate: { type: Date, required: true },
    adType: {
      type: String,
      required: true,
      enum: ["Feed", "Suggestion", "Fullscreen"],
    },
  },
  { timestamps: true }
);

const Ad = mongoose.model("Ad", adSchema);

module.exports = Ad;
