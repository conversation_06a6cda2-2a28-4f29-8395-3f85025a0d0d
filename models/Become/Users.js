const mongoose = require("mongoose");

const Schema = mongoose.Schema;

const businessLocationSchema = new Schema({
  type: {
    type: String,
    default: "Point",
    enum: ["Point"],
  },
  coordinates: {
    type: [Number],
    required: true,
  },
});

const businessDetailsSchema = new Schema({
  name: { type: String, required: true },
  location: businessLocationSchema,
  address: { type: String, required: false },
  category: { type: String, required: true },
  city: { type: String, required: true },
  taxNumber: { type: String, required: true },
  businessEmail: { type: String, required: true },
  businessMobile: { type: String, required: true },
  businessTel: { type: String },
  photos: [String],
});

const legalDetailsSchema = new Schema({
  email: { type: String, required: true },
  fullName: { type: String, required: true },
  lastName: { type: String, required: true },
  jobPosition: { type: String, required: true },
  mobile: { type: String, required: true },
});

const userSchema = new Schema(
  {
    businessDetails: businessDetailsSchema,
    legalDetails: legalDetailsSchema,
  },
  { timestamps: true }
);

const Partnerbusinesses = mongoose.model("Partnerbusinesses", userSchema);

module.exports = Partnerbusinesses;
