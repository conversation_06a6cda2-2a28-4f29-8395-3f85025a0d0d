const mongoose = require("mongoose");
const mongoose_delete = require("mongoose-delete");

const Schema = new mongoose.Schema(
  {
    partnerId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "partner",
      index: true,
    },
    title: {
      type: String,
    },
    body: {
      type: String,
    },
    type: {
      type: String,
      enum: ["INBOX", "PUSH", "ADS"],
      default: "ADS",
    },
    seen: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

Schema.plugin(mongoose_delete, {
  overrideMethods: ["find", "findOne", "findOneAndUpdate", "update"],
});
const partnerNotification = mongoose.model("partnerNotification", Schema);

module.exports = partnerNotification;
