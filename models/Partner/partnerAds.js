const mongoose = require("mongoose");
const mongoose_delete = require("mongoose-delete");

const partnerAdsSchema = new mongoose.Schema(
  {
    partnerId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "partner",
      index: true,
    },
    name: {
      type: String,
    },
    image: {
      type: String,
    },
    title: {
      type: String,
    },
    body: {
      type: String,
    },
    addressObject: {
      address: {
        type: String,
      },
      city: {
        type: String,
      },
      country: {
        type: String,
      },
      house_number: {
        type: String,
      },
      postcode: {
        type: String,
      },
      street: {
        type: String,
      },
    },
    location: {
      type: {
        type: String,
        enum: ["Point"],
      },
      coordinates: {
        type: [Number],
        index: "2dsphere",
      },
    },
    cities: [],
    releaseDate: {
      type: Date,
      index: true,
    },
    releaseTime: {
      type: String,
    },
    userType: {
      type: String,
      enum: ["PARTNER", "ADMIN"],
    },
    adType: {
      type: String,
      enum: ["Feed", "Suggestion", "Fullscreen"],
      default: "Feed",
      index: true,
    },
    reason: {
      type: String,
    },
    status: {
      type: String,
      enum: [
        "Requested",
        "Approved",
        "Rejected",
        "Cancelled",
        "Live",
        "Ended",
        "WithDrawed",
      ],
      default: "Requested",
      index: true,
    },
  },
  {
    timestamps: true,
  }
);

partnerAdsSchema.plugin(mongoose_delete, {
  overrideMethods: ["find", "findOne", "findOneAndUpdate", "update"],
});
const PartnerAds = mongoose.model("partnerAds", partnerAdsSchema);

module.exports = PartnerAds;
