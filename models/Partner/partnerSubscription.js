const mongoose = require("mongoose");
const mongoose_delete = require("mongoose-delete");

const Schema = new mongoose.Schema(
  {
    partnerId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "partner",
      required: true,
    },
    expireAt: {
      type: Date,
    },
    canceledAt: {
      type: Date,
      default: null,
    },
    endAt: {
      type: Date,
      default: null,
    },
    startAt: {
      type: Date,
      default: null,
    },
    subscriptionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Subscription",
    },
    stripeSubscriptionId: {
      type: String,
    },
    stripePaymentId: {
      type: String,
      unique: false,
    },
    usedVoucher: {
      type: String,
      default: null,
    },
    timePeriod: {
      type: Number,
    },
    isActive: {
      type: Boolean,
      default: false,
    },
    isCancel: {
      type: Boolean,
      default: false,
    },
    cancelText: {
      type: String,
    },
  },
  { timestamps: true }
);

Schema.plugin(mongoose_delete, {
  overrideMethods: ["find", "findOne", "findOneAndUpdate", "update"],
});
const PartnerSubscription = mongoose.model("partnerSubscription", Schema);

module.exports = PartnerSubscription;
