const mongoose = require("mongoose");
const mongoosePaginate = require("mongoose-paginate-v2");
const mongooseDelete = require("mongoose-delete");

const chatSchema = new mongoose.Schema({
  // Track which users have deleted this chat
  deletedBy: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  ],
  invitationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "invitation",
    required: true,
  },
  participants: [
    {
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
      },
      joinedAt: {
        type: Date,
        default: () => new Date(), // Always UTC
        index: true, // Index for efficient message filtering
      },
    },
  ],
  isGroupChat: {
    type: Boolean,
    default: false,
  },
  messages: [
    {
      sender: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
      },
      content: {
        type: String,
        required: true,
      },
      isAudio: {
        type: Boolean,
        default: false,
      },
      audioUrl: {
        type: String,
        default: null,
      },
      isInfo: {
        type: Boolean,
        default: false,
      },
      timestamp: {
        type: Date,
        default: () => new Date(), // Always UTC
      },
      read: [
        {
          user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User",
          },
          readAt: {
            type: Date,
            default: null,
          },
        },
      ],
    },
  ],
  premiumEarlyStart: {
    type: Boolean,
    default: false,
  },
  chatOpenTime: {
    type: Date,
  },
  chatCloseTime: {
    type: Date,
  },
  active: {
    type: Boolean,
    default: true,
  },
  createdAt: {
    type: Date,
    default: () => new Date(), // Always UTC
  },
  updatedAt: {
    type: Date,
    default: () => new Date(), // Always UTC
  },
});

chatSchema.plugin(mongoosePaginate);
chatSchema.plugin(mongooseDelete, { deletedAt: true, overrideMethods: true });

module.exports = mongoose.model("Chat", chatSchema);
